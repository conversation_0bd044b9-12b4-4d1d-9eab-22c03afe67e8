<template>
    <div>
    <DxLoadPanel
      v-model:visible="loadingVisible"
      :show-indicator="true"
      :show-pane="true"
      :shading="true"
      shading-color="rgba(0,0,0,0.4)"
    />
    <div style="border: 1px solid rgba(58, 175, 169, 1); border-radius: 1rem; padding: 10px; margin-bottom: 10px;">
    <DxButton
      :width="200"
      text="Create New Site"
      type="default"
      styling-mode="contained"
      icon="plus"
      @click="CreateNewSiteClicked"
    />
    <DxDataGrid
      style="padding-top: 10px; padding-bottom: 10px;"
      :data-source="dataSource"
      :column-hiding-enabled="true"
      :column-auto-width="true"
      :row-alternation-enabled="true"
      :show-borders="true"
      :word-wrap-enabled="true"
      @row-updated="updateSite"
    >
      <DxEditing :allow-updating="true" />
      <DxPaging :page-size="5"/>
      <DxFilterRow
        :visible="true"
      />
      <DxColumn
        data-field="site_name"
        caption="Site Name"
      />
      <DxColumn
        data-field="site_address"
        caption="Address"
      />
      <DxColumn
        data-field="site_state"
        caption="State"
      />
      <DxColumn
        data-field="site_city"
        caption="City"
      />
      <DxColumn
        data-field="site_zip"
        caption="Zip"
      />
    </DxDataGrid>
    </div>
    <div style="border: 1px solid rgba(58, 175, 169, 1); border-radius: 1rem; padding: 10px;">
    <DxButton
      :width="220"
      text="Create New Contact"
      type="default"
      styling-mode="contained"
      icon="plus"
      @click="CreateNewContactClicked"
    />
    <DxDataGrid
      style="padding-top: 10px;"
      :data-source="userDataSource"
      :column-hiding-enabled="true"
      :column-auto-width="true"
      :row-alternation-enabled="true"
      :show-borders="true"
      :word-wrap-enabled="true"
    >
      <DxPaging :page-size="5"/>
      <DxFilterRow
        :visible="true"
      />
      <DxColumn
        data-field="customers_first_name"
        caption="First Name"
      />
      <DxColumn
        data-field="customers_last_name"
        caption="Last Name"
      />
      <DxColumn
        data-field="customers_email"
        caption="Email Address"
      />
      <DxColumn
        data-field="customers_cell_phone_number"
        caption="Cell Phone #"
      />
      <DxColumn
        data-field="customers_office_phone_number"
        caption="Office Phone #"
      />
    </DxDataGrid>
    </div>

    <DxPopup v-if="isMobile"
      v-model:visible="popupVisible"
      :drag-enabled="false"
      :hide-on-outside-click="false"
      :show-close-button="true"
      :show-title="true"
      height="90%"
      width="90%"
      title="Create Site"
    >
      <div>
        <div>
          Site Name:
        </div>
        <div class="spaceBetweenLabelAndInput">
          <DxTextBox
            v-model:value="name"
          />
        </div>
      </div>
      <div class="spaceBetweenFields">
        <div>
          Address:
        </div>
        <div class="spaceBetweenLabelAndInput">
          <DxTextBox
            v-model:value="address"
          />
        </div>
      </div>
      <div class="spaceBetweenFields">
        <div>
          State:
        </div>
        <div class="spaceBetweenLabelAndInput">
          <DxTextBox
            v-model:value="state"
          />
        </div>
      </div>
      <div class="spaceBetweenFields">
        <div>
          City:
        </div>
        <div class="spaceBetweenLabelAndInput">
          <DxTextBox
            v-model:value="city"
          />
        </div>
      </div>
      <div class="spaceBetweenFields">
        <div>
          Zip Code:
        </div>
        <div class="spaceBetweenLabelAndInput">
          <DxTextBox
            v-model:value="zip"
          />
        </div>
      </div>
      <div class="btnForCreateNew">
        <DxButton
          :width="170"
          text="Create Site"
          type="default"
          styling-mode="contained"
          @click="CreateNewSite"
        />
      </div>
    </DxPopup>
    <DxPopup v-else
      v-model:visible="popupVisible"
      :drag-enabled="false"
      :hide-on-outside-click="false"
      :show-close-button="true"
      :show-title="true"
      height="90%"
      width="50%"
      title="Create Site"
    >

      <div class="dx-fieldset">
          <div class="dx-field">
            <div class="dx-field-label">Site Name:</div>
            <div class="dx-field-value">
              <DxTextBox
                v-model:value="name"
              />
            </div>
          </div>
          <div class="dx-field">
            <div class="dx-field-label">Address:</div>
            <div class="dx-field-value">
              <DxTextBox
                v-model:value="address"
              />
            </div>
          </div>
          <div class="dx-field">
            <div class="dx-field-label">State:</div>
            <div class="dx-field-value">
              <DxTextBox
                v-model:value="state"
              />
            </div>
          </div>
          <div class="dx-field">
            <div class="dx-field-label">City:</div>
            <div class="dx-field-value">
              <DxTextBox
                v-model:value="city"
              />
            </div>
          </div>
          <div class="dx-field">
            <div class="dx-field-label">Zip Code:</div>
            <div class="dx-field-value">
              <DxTextBox
                v-model:value="zip"
              />
            </div>
          </div>
          <div class="dx-field">
            <div class="dx-field-label"></div>
            <div class="dx-field-value">
              <DxButton
                :width="170"
                text="Create Site"
                type="default"
                styling-mode="contained"
                @click="CreateNewSite"
              />
            </div>
        </div>
      </div>  

    </DxPopup>

    <DxPopup v-if="isMobile"
      v-model:visible="userPopupVisible"
      :drag-enabled="false"
      :hide-on-outside-click="false"
      :show-close-button="true"
      :show-title="true"
      height="90%"
      width="90%"
      title="Create Contact"
    >
      <div>
        <div>
          First Name:
        </div>
        <div class="spaceBetweenLabelAndInput">
          <DxTextBox
            v-model:value="firstName"
          />
        </div>
      </div>
      <div class="spaceBetweenFields">
        <div>
          Last Name:
        </div>
        <div class="spaceBetweenLabelAndInput">
          <DxTextBox
            v-model:value="lastName"
          />
        </div>
      </div>
      <div class="spaceBetweenFields">
        <div>
          Email Address:
        </div>
        <div class="spaceBetweenLabelAndInput">
          <DxTextBox
            v-model:value="emailAddress"
          />
        </div>
      </div>
      <div class="spaceBetweenFields">
        <div>
          (optional) Cell Phone #: (************)
        </div>
        <div class="spaceBetweenLabelAndInput">
          <DxTextBox
            v-model:value="cellPhoneNumber"
          />
        </div>
      </div>
      <div class="spaceBetweenFields">
        <div>
          (optional) Office Phone #:
        </div>
        <div class="spaceBetweenLabelAndInput">
          <DxTextBox
            v-model:value="officePhoneNumber"
          />
        </div>
      </div>
      <div class="btnForCreateNew">
        <DxButton
          :width="170"
          text="Create Contact"
          type="default"
          styling-mode="contained"
          @click="CreateNewContact"
        />
      </div>
    </DxPopup>
    <DxPopup v-else
      v-model:visible="userPopupVisible"
      :drag-enabled="false"
      :hide-on-outside-click="false"
      :show-close-button="true"
      :show-title="true"
      height="90%"
      width="50%"
      title="Create Contact"
    >
      <div class="dx-fieldset">
        <div class="dx-field">
          <div class="dx-field-label">First Name:</div>
          <div class="dx-field-value">
            <DxTextBox
              v-model:value="firstName"
            />
          </div>
        </div>
        <div class="dx-field">
          <div class="dx-field-label">Last Name:</div>
          <div class="dx-field-value">
            <DxTextBox
              v-model:value="lastName"
            />
          </div>
        </div>
        <div class="dx-field">
          <div class="dx-field-label">Email Address:</div>
          <div class="dx-field-value">
            <DxTextBox
              v-model:value="emailAddress"
            />
          </div>
        </div>
        <div class="dx-field">
          <div class="dx-field-label">(optional)Cell Phone #: (************)</div>
          <div class="dx-field-value">
            <DxTextBox
              v-model:value="cellPhoneNumber"
            />
          </div>
        </div>
        <div class="dx-field">
          <div class="dx-field-label">(optional)Office Phone #:</div>
          <div class="dx-field-value">
            <DxTextBox
              v-model:value="officePhoneNumber"
            />
          </div>
        </div>
        <div class="dx-field">
          <div class="dx-field-label"></div>
          <div class="dx-field-value">
            <DxButton
              :width="170"
              text="Create Contact"
              type="default"
              styling-mode="contained"
              @click="CreateNewContact"
            />
          </div>
        </div>

      </div>
    </DxPopup>
    </div>
</template>

<script setup>
import { ref, defineProps } from 'vue';
import databaseName from '../myFunctions/databaseName';
import { useRouter } from 'vue-router';
import axios from 'axios';
import notify from 'devextreme/ui/notify';
import DxLoadPanel from 'devextreme-vue/load-panel';
import DxButton from 'devextreme-vue/button';
import DxPopup from 'devextreme-vue/popup';
import DxTextBox from 'devextreme-vue/text-box';
import auth from "../auth";
import {
  DxDataGrid,
  DxColumn,
  DxPaging,
  DxFilterRow,
  // DxScrolling,
  DxEditing
} from 'devextreme-vue/data-grid';

const router = useRouter();

const popupVisible = ref(false);
const userPopupVisible = ref(false);

const name = ref("");
const address = ref("");
const state = ref("");
const city = ref("");
const zip = ref("");

const firstName = ref("");
const lastName = ref("");
const emailAddress = ref("");
const cellPhoneNumber = ref("");
const officePhoneNumber = ref("");

const loadingVisible = ref(false);
const props = defineProps({
  templateData: Object
});

const isMobile = ref(false);

const dataSource = ref([]);
const userDataSource = ref([]);
isMobile.value = databaseName.getScreenSizeSmall();

const CreateNewSiteClicked = () => {
  name.value = "";
  address.value = "";
  state.value = "";
  city.value = "";
  zip.value = "";

  popupVisible.value = true;
}

const CreateNewContactClicked = () => {
  firstName.value = "";
  lastName.value = "";
  emailAddress.value = "";
  cellPhoneNumber.value = "";
  officePhoneNumber.value = "";

  userPopupVisible.value = true;
}

const CreateNewContact = () => {
  auth.getUser().then(user=>{
    if(firstName.value == "")
    {
      notify('You Must Include A First Name', 'warning', 7000);
      return;
    }
    if(lastName.value == "")
    {
      notify('You Must Include A Last Name', 'warning', 7000);
      return;
    }
    if(emailAddress.value == "")
    {
      notify('You Must Include An Email Address', 'warning', 7000);
      return;
    }
    if(cellPhoneNumber.value != "")
    {
      let strSplit = cellPhoneNumber.value.split('-');
      if(strSplit.length != 3)
      {
        notify('Cell Phone Number Must Be In The Following Format: ************', 'warning', 10000);
        return;
      }
      else
      {
        if(strSplit[0].length != 3)
        {
          notify('Cell Phone Number Must Be In The Following Format: ************', 'warning', 10000);
          return;
        }
        else if(strSplit[1].length != 3)
        {
          notify('Cell Phone Number Must Be In The Following Format: ************', 'warning', 10000);
          return;
        }
        else if(strSplit[2].length != 4)
        {
          notify('Cell Phone Number Must Be In The Following Format: ************', 'warning', 10000);
          return;
        }
      }
    }

    let UserJSON = {
      customers_first_name: firstName.value,
      customers_last_name: lastName.value,
      customer_name: props.templateData.data.customer_name,
      customers_username: firstName.value.split('')[0] + lastName.value,
      customers_email: emailAddress.value,
      customers_cell_phone_number: cellPhoneNumber.value == "" ? null : cellPhoneNumber.value,
      customers_office_phone_number: officePhoneNumber.value == "" ? null : officePhoneNumber.value
    };

    loadingVisible.value = true;
    axios({
      method: 'POST',
      url: 'api/Auth/CustomerUser',
      data: UserJSON,
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`,
        'Content-Type': 'application/json'
      }
    })
    .then(resp=>{
      console.log(resp);
      GetData();
    })
    .catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify(error.response.data.message, 'error', 10000);
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    })
    .finally(()=>{
      loadingVisible.value = false;
      popupVisible.value = false;
    })

  })
}

const updateSite = (e) =>{
  auth.getUser().then(user=>{
    if(e.data.site_name == "")
    {
      notify('You Must Include A Site Name', 'warning', 7000);
      GetData();
      return;
    }
    if(e.data.site_address == "")
    {
      notify('You Must Include An Address', 'warning', 7000);
      GetData();
      return;
    }
    if(e.data.site_state == "")
    {
      notify('You Must Include A State', 'warning', 7000);
      GetData();
      return;
    }
    if(e.data.site_city == "")
    {
      notify('You Must Include A City', 'warning', 7000);
      GetData();
      return;
    }
    if(e.data.site_zip == "")
    {
      notify('You Must Include A Zip Code', 'warning', 7000);
      GetData();
      return;
    }

    axios({
      method: 'PATCH',
      url: 'api/Customer/Site',
      data: e.data,
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`,
        'Content-Type': 'application/json'
      }
    })
    .then(resp=>{
      console.log(resp);
    })
    .catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify(error.response.data.message, 'error', 10000);
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    }).finally(()=>{
      GetData();
    })

  })
}

const CreateNewSite = () => {
  auth.getUser().then(user=>{
    

    if(name.value == "")
    {
      notify('You Must Include A Site Name', 'warning', 7000);
      return;
    }
    if(address.value == "")
    {
      notify('You Must Include An Address', 'warning', 7000);
      return;
    }
    if(state.value == "")
    {
      notify('You Must Include A State', 'warning', 7000);
      return;
    }
    if(city.value == "")
    {
      notify('You Must Include A City', 'warning', 7000);
      return;
    }
    if(zip.value == "")
    {
      notify('You Must Include A Zip Code', 'warning', 7000);
      return;
    }

    loadingVisible.value = true;

    let SiteJSON = {
      site_name: name.value,
      customer_name: props.templateData.data.customer_name,
      site_address: address.value,
      site_state: state.value,
      site_city: city.value,
      site_zip: zip.value
    };

    axios({
      method: 'POST',
      url: 'api/Customer/Site',
      data: SiteJSON,
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`,
        'Content-Type': 'application/json'
      }
    })
    .then(resp=>{
      console.log(resp);
      GetData();
    })
    .catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify(error.response.data.message, 'error', 10000);
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    })
    .finally(()=>{
      loadingVisible.value = false;
      popupVisible.value = false;
    })
  })
}

const GetData = () => {
  auth.getUser().then(user=>{
    let customerName = props.templateData.data.customer_name;
    loadingVisible.value = true;
    axios({
      method: 'GET',
      url: `api/Customer/Site?CustomerName=${customerName}`,
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`
      }
    })
    .then(resp=>{
      dataSource.value = resp.data.data;



      axios({
        method: 'GET',
        url: `api/Auth/CustomerUser?CustomerName=${customerName}`,
        headers:{
          'Authorization': `Bearer ${user.data.auth_token}`
        }
      })
      .then(resp=>{
        userDataSource.value = resp.data.data;
      })
      .catch(error=>{
        console.error(error);
        console.log(error);
        if(error.response.status == 401)
        {
          notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
          router.push('/login-form');
        }
        else if(error.response.status == 500)
        {
          notify(error.response.data.message, 'error', 10000);
        }
        else{
          notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
        }
      })




    })
    .catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify(error.response.data.message, 'error', 10000);
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    })
    .finally(()=>{
      loadingVisible.value = false;
    })
  });
}
GetData();


</script>
<style lang="scss">
.spaceBetweenFields {
  margin-top: 25px;
}
.spaceBetweenLabelAndInput {
  margin-top: 5px;
}
.btnForCreateNew {
  float: right;
  margin-top: 25px;
}
</style>