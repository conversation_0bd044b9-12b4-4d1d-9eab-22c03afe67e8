<template>
  <div class="container">

    <div style="font-size: large; margin-top: 25px;">Connections by Type</div>
    
    <!-- <div v-if="loading" class="loading"> Loading warehouse connections...</div> -->
    <div class="connections-list">

			<!-- Groups -->
      <div v-for="(connections, type) in groupedConnections" :key="type" class="connection-group" @click="toggleGroup(type)">
        
				<div class="group-header">
					<div class="status-indicator" :class="getStatusClass(type)"></div>
					<div class="group-title">{{ type }}</div>
          <div class="connection-stats"> {{ countOnlineByType(type) }} / {{ countTotalByType(type) }} Active </div>
					
          <div class="expand-icon">{{ expandedGroups.includes(type) ? '▼' : '►' }}</div>
        </div>

        <!-- Group items -->
        <div v-if="expandedGroups.includes(type)" class="group-items">

					<table class="conn-table">
            <thead>
              <tr>
								<th></th><th>Name</th><th v-if="!isMobile">Protocol Note</th><th>General Note</th>
							</tr>
            </thead>
            <tbody>
							<template v-for="connection in connections" :key="connection.idmonitor_warehouse_connections">
								<tr>
									<td width="35"><div class="status-indicator" :class="[ connection.warehouse_connection_alive? 'status-all-active' : 'status-all-inactive' ] "></div></td>
									<td><span class="link" @click.stop.prevent="onConnectionClick(connection.idmonitor_warehouse_connections)" >{{ connection.connection_name }}</span></td>
									<td v-if="!isMobile">{{ connection.warehouse_connection_protocol_note }}</td>
									<td>{{ connection.warehouse_connection_general_note }}</td>
								</tr>
								<!-- Footer row for each connection... on the fence about this -->
								<!-- <tr class="row-footer">
									<td colspan="4">
										Additional notes for {{ connection.connection_name }}: {{ connection.additionalInfo || 'No additional information' }}
									</td>
								</tr> -->
							</template>
            </tbody>
          </table>

        </div>

      </div>
			<DxLoadPanel
				:visible="loading"
				shading-color="rgba(0,0,0,0.0)"
				:show-pane="true"
				:container="'.slide-panel-content'"
				:position="{ of: '.connections-list' }"
			/>

    </div>

		<DxPopup
			:visible="popupStatsVisible"
			:drag-enabled="false"
			:hide-on-outside-click="false"
			:show-close-button="true"
			:show-title="true"
			:onHidden='onPopupHidden'
			:height="isMobile? '100%': '90%'"
			:width="isMobile? '100%': '90%'"
			:title="popupViewConnectionTitle">
			
			<template #content>
				<ConnectionStats
					:siteID="connections[0].idcustomers_sites"
					:connectionID="selectedConnectionID"
					:isMobile="isMobile">
				</ConnectionStats>
			</template>

		</DxPopup>
 
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch ,defineProps, onUnmounted} from 'vue';
import { DxPopup, DxButton } from 'devextreme-vue';
import { DxLoadPanel } from 'devextreme-vue/load-panel';
import ConnectionStats from  '@/components/connection-stats.vue';

const props = defineProps({
  connections: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
	isMobile:  {
		type: Boolean,
    default: false
  },
});

const expandedGroups = ref([]);
const popupStatsVisible = ref(false);
const selectedConnectionID = ref(null)
const popupViewConnectionTitle = ref("");

onMounted(() => {

});


// Group connections by warehouse_connection_type
const groupedConnections = computed(() => {
  const grouped = {};
  props.connections.forEach(connection => {
    const type = connection.warehouse_connection_type;
    if (!grouped[type]) {
      grouped[type] = [];
    }
    grouped[type].push(connection);
  });
  
  // Sort the object by keys
  return Object.keys(grouped)
    .sort()
    .reduce((result, key) => {
      result[key] = grouped[key];
      return result;
    }, {});
});


const onConnectionClick = (connectionID) => {

	const conn = props.connections.find(e => e.idmonitor_warehouse_connections === connectionID)
	console.log(conn.connection_name)
	popupViewConnectionTitle.value = `Connection: ${conn.connection_name} Individual Stats`;
	selectedConnectionID.value = connectionID
	popupStatsVisible.value = true;

}

const onPopupHidden = () => {
	popupStatsVisible.value = false;
};

const getStatusClass = (type) => {
	const active = countOnlineByType(type)
	const total = countTotalByType(type) 

		return (active === total)
		? 'status-all-active'  // All devices active
		: active === 0 
			? 'status-all-inactive'  // No devices active
			: 'status-partial-active'  // Some devices active
};

// Toggle group expansion
const toggleGroup = (type) => {
  if (expandedGroups.value.includes(type)) {
    expandedGroups.value = expandedGroups.value.filter(group => group !== type);
  } else {
    expandedGroups.value.push(type);
  }
};

// Count online connections by type
const countOnlineByType = (type) => {
  if (!groupedConnections.value[type]) return 0;
  return groupedConnections.value[type].filter(conn => conn.warehouse_connection_alive).length;
};

// Count offline connections by type
const countOfflineByType = (type) => {
  if (!groupedConnections.value[type]) return 0;
  return groupedConnections.value[type].filter(conn => !conn.warehouse_connection_alive).length;
};

// Count total connections by type
const countTotalByType = (type) => {
  if (!groupedConnections.value[type]) return 0;
  return groupedConnections.value[type].length;
};

watch(() => props.connections, () => {
	expandedGroups.value = [];
});




</script>

<style lang="scss" scoped>
  @import "../themes/generated/variables.additional.scss";

	.container{
		color: rgba($base-text-color, .75)!important;
	}
	.connections-list {
		display: flex;
		flex-direction: column;
		gap: 15px;
	}
	.connection-group {
		background-color: rgba($base-bg-dark, .65);

		padding: 15px;
		border-radius: 10px;
		cursor: pointer;
	}
	.group-header {
		display: flex;
		gap: 10px;
		
	}
	.status-indicator {
		width: 15px;
		height: 15px;
		border-radius: 50%;
		position: relative;
		transition: all 0.3s ease;
		
	}

	.status-all-active {
		background: conic-gradient( from 0deg, #4CAF50 0deg 360deg );
		outline: 1px solid #4CAF50;;
	}

	.status-partial-active {
		background: conic-gradient( from 0deg, transparent 0deg 180deg, #ffd700 180deg 360deg );
		outline: 1px dotted #ffd700;
		
	}

	.status-all-inactive {
		background: #FF5252;
		outline: 1px solid #FF5252;;
	}


	.conn-table {
		width: 100%;
		border-collapse: collapse;
		margin-top: 10px;
	}

	.conn-table  th, .conn-table td {
		// border: 1px solid #dee2e6;
		padding: 8px 12px;
		text-align: left;
	}

	.link {
		text-decoration: underline;
		color: $base-accent
	}
</style>