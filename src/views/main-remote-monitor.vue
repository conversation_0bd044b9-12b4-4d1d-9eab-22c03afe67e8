<template>
  <div>
    <h2 class="content-block">Remote Monitor {{ SiteName }}</h2>

    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <DxLoadPanel
          :height="500"
          :width="700"
          :visible="loadingVisible"
          :show-indicator="true"
          :show-pane="true"
          :shading="true"
          shading-color="rgba(0,0,0,0.4)"
        />
        
        <div v-if="showMainData"><!-- Display the main data for selecting a site to view more details about -->
          <div class="groupContainerGridMain">
            <DxButton
              style="margin-right: 10px;"
              text="Refresh"
              type="success"
              styling-mode="contained"
              icon="refresh"
              @click="GetMainSiteData"
            /> 
            <DxSelectBox
              :items="statusClearedSelectData"
              v-model:value="statusSelected"
              display-expr="disp"
              value-expr="val"
              @value-changed="filterDataGrid"
              styling-mode="underlined"
              style="margin-left: 15px;"
              width="150"
            />
          </div>

          <DxDataGrid
            style="padding-top: 10px;"
            ref="dataGridMain"
            :data-source="MainSiteData"
            :column-hiding-enabled="true"
            :column-auto-width="true"
            :row-alternation-enabled="true"
            :show-borders="true"
            :word-wrap-enabled="true"
            height="500px"
          >
            <DxScrolling mode="infinite"/>
            <DxFilterRow
              :visible="true"
            />
            <DxColumn
              data-field="site_name"
              caption="Site"
              cell-template="siteStatusTemplate"
              :sort-index="1"
              sort-order="asc"
            />
            <DxColumn
              data-field="koz_tracks_remote_stats"
              caption="KOZ Tracks Site"
              cell-template="showConnectionsTemplate"
              alignment="center"
              :sort-index="0"
              sort-order="desc"
            />
            <DxColumn
              data-field="customer_name"
              caption="Customer"
            />
            <DxColumn
              data-field="formatted_address"
              caption="Address"
            />

            <template #siteStatusTemplate="{data}">
              <div class="groupContainerGridMain">
                <div :class="getSiteStatusColor(data)"></div>
                <div>{{data.text}}</div>
              </div>
            </template>

            <template #showConnectionsTemplate="{data}">
              <DxButton v-if="data.data.koz_tracks_remote_stats"
                :width="200"
                text="View Connections"
                type="default"
                styling-mode="contained"
                @click="ShowSiteConnections(data.data.idcustomers_sites, data.data.site_name)"
              />
              <div v-else>
                KOZ Not Monitoring
              </div>

            </template>
            <!-- <DxMasterDetail
              :enabled="true"
              template="templateForExtraSiteData"
            />
            <template #templateForExtraSiteData>
              <div style="display: flex; justify-content: space-evenly;">
                <div>
                  <DxButton
                  text="Audit Log"
                  type="success"
                  styling-mode="contained"
                  @click="clickToOpenLogPopup"
                />
                </div>
                <div>
                  <DxButton
                  text="Error Log"
                  type="default"
                  styling-mode="contained"
                  @click="clickToOpenLogPopup"
                />
                </div>
                <div>
                  <DxButton
                  text="Alarm Log"
                  type="danger"
                  styling-mode="contained"
                  @click="clickToOpenLogPopup"
                />
                </div>
              </div>
            </template> -->

          </DxDataGrid>
          <div class="keyGroupContainerMain">
            <div class="on-main"></div>
            <div>Site Is Tracked By KOZ And All Connections Are Alive</div>
          </div>
          <div class="keyGroupContainerMain">
            <div class="off-main"></div>
            <div v-if="isMobile">Site Is Tracked By KOZ And 1 Or More<br/> Connections Are Not Alive</div>
            <div v-else>Site Is Tracked By KOZ And 1 Or More Connections Are Not Alive</div>
          </div>
          <div class="keyGroupContainerMain">
            <div class="na-main"></div>
            <div>Site Is NOT Tracked By KOZ, Connection Status Unknown</div>
          </div>
          <DxPopup
            v-model:visible="logPopup"
            :drag-enabled="false"
            :hide-on-outside-click="false"
            :show-close-button="true"
            :show-title="true"
            height="80%"
            width="80%"
          >
            <div>
              <h5>Hello, I Will Be Audit Or Error Or Alarm Logs Hopefully In The Future</h5>
              <div>
                <!-- :data-source="group.TimeRanges" -->
                <DxDataGrid
                  :height="400"
                  :column-hiding-enabled="true"
                  :row-alternation-enabled="true"
                  :show-borders="true"
                  :word-wrap-enabled="true"
                  :column-auto-width="true"
                  @editor-preparing="modifySearchPanelStyleLogsPopup"
                >
                  <DxPaging :page-size="10"/>
                  <DxPager
                    :show-page-size-selector="true"
                    :allowed-page-sizes="[10 , 20, 40]"
                    :show-info="true" 
                  />
                  <!-- <DxExport   
                    :enabled="true"
                  /> -->
                  <DxFilterRow
                    :visible="true"
                  />
                  <DxSearchPanel
                    :visible="true"
                    :width="170"
                    placeholder="Filter Results..."
                  />
                </DxDataGrid>
              </div>
            </div>

          </DxPopup>
          
        </div>
        <div v-else> <!-- connection data is here in the v-else div -->
          <div style="display: flex; justify-content: space-between;">
            <DxButton
              text="Go Back To Sites"
              type="default"
              styling-mode="contained"
              icon="back"
              @click="GoBackToSitesClicked"
            />
            <DxButton
              text="Refresh"
              type="success"
              styling-mode="contained"
              icon="refresh"
              @click="GetSiteConnectionsData"
            />
          </div>
          <h5 style="text-align: center; margin-bottom: 5px;">
            Note: Click Connection to Display Last {{QueryHours}} Hours of Stats
          </h5>
          <DxDataGrid
            style="padding-top: 10px;"
            :data-source="siteConnectionsDS"
            :column-hiding-enabled="true"
            :column-auto-width="true"
            :row-alternation-enabled="true"
            :show-borders="true"
            :word-wrap-enabled="true"
            height="400px"
            @cell-click="connectionNameClicked"
          >
            <DxScrolling mode="infinite"/>
            <DxFilterRow
              :visible="true"
            />
            <DxColumn
              data-field="connection_name"
              caption="Connection"
              cell-template="siteConnectionTemplate"
            />
            <DxColumn
              data-field="warehouse_connection_type"
              caption="Connection Type"
            />
            <DxColumn
              data-field="warehouse_connection_protocol_note"
              caption="Protocol Note"
            />
            <DxColumn
              data-field="warehouse_connection_general_note"
              caption="General Note"
            />

            <template #siteConnectionTemplate="{data}">
              <div class="groupContainerGridMain">
                <div :class="getConnectionStatusColorMain(data)"></div>
                <div class="scannerGridLinkMain">{{data.text}}</div>
              </div>
            </template>

          </DxDataGrid>
          <h5 style="text-align: center; margin-bottom: 5px;">
            Last 12 Hours Of Scanner Stats For Site:
          </h5>
          <DxDataGrid
            style="padding-top: 10px;"
            :data-source="siteScannerInfoDS"
            :column-hiding-enabled="true"
            :column-auto-width="true"
            :row-alternation-enabled="true"
            :show-borders="true"
            :word-wrap-enabled="true"
            height="400px"
          >
            <DxScrolling mode="infinite"/>
            <DxFilterRow
              :visible="true"
            />
            <DxColumn
              data-field="connection_name"
              caption="Scanner"
            />
            <DxColumn v-for="(val) in siteScannerColumnsDS" :key="val.stat_name"
              :data-field="val.stat_name"
              alignment="center"
            />

          </DxDataGrid>
        </div>

        <DxPopup
          v-model:visible="popupViewConnectionVisible"
          :drag-enabled="false"
          :hide-on-outside-click="false"
          :show-close-button="true"
          :show-title="true"
          height="90%"
          width="90%"
          :title="popupViewConnectionTitle"
        >
          <div :class="showMobileViewOrNotMainMonitor.viewConnectionPopupSearchParams">
            <div :class="showMobileViewOrNotMainMonitor.viewConnectionPopupSingleSearchParams">
              <strong>End Datetime</strong>
              <DxDateBox
                v-model:value="popupStopTime"
                type="datetime"
                styling-mode="underlined"
              />
            </div>
            <div :class="showMobileViewOrNotMainMonitor.viewConnectionPopupSingleSearchParams">
              <strong>Past Hours</strong>
              <DxNumberBox
                :max="24"
                :min="1"
                :show-spin-buttons="true"
                v-model:value="QueryHours"
                styling-mode="underlined"
              />
            </div>
            <div :class="showMobileViewOrNotMainMonitor.viewConnectionPopupSingleSearchParams">
              <DxButton
                :width="150"
                text="Search"
                type="default"
                styling-mode="contained"
                @click="ReFilterButtonPressed"
              />
            </div>
          </div>
          <DxTabPanel
            :key="popupSelectionChanged"
            :data-source="popupViewConnectionDS.stat_groups"
            v-model:selected-index="popupGroupIndex"
            :loop="true"
            :animation-enabled="false"
            :swipe-enabled="false"
          >
            <template #title="{ data: group }">
              <span>{{ group.stat_group }}</span>
            </template>
            <template #item="{ data: group }">
              <div>
                <DxDataGrid
                  :height="300"
                  :data-source="group.TimeRanges"
                  :column-hiding-enabled="showMobileViewOrNotMainMonitor.popupTimeRangeGridColumnHide"
                  :row-alternation-enabled="true"
                  :show-borders="true"
                  :word-wrap-enabled="true"
                  :column-auto-width="true"
                >
                  <DxColumn
                    :width="showMobileViewOrNotMainMonitor.popupGridTimeRangeColumnsWidth"
                    data-field="StartDatetime"
                    caption="Start"
                    data-type="datetime"
                    :fixed="true" 
                    fixed-position="left"
                  />
                  <DxColumn
                    :width="showMobileViewOrNotMainMonitor.popupGridTimeRangeColumnsWidth"
                    data-field="EndDatetime"
                    caption="End"
                    data-type="datetime"
                    :fixed="true" 
                    fixed-position="left"
                  />
                  <DxColumn v-for="(val) in group.stat_columns" :key="val.stat_name"
                    :data-field="val.stat_name"
                    :caption="val.stat_name_disp"
                    alignemnt="center"
                  />
                  <DxSummary>
                    <DxTotalItem v-for="(val) in group.stat_columns" :key="val.stat_name"
                      :column="val.stat_name"
                      summary-type="sum"
                      display-format="{0}"
                      css-class="gridTotalsMain"
                    />
                  </DxSummary>
                </DxDataGrid>
                <DxChart
                  :data-source="group.TimeRanges"
                  title="Group Stats"
                >
                  <DxTooltip
                    :enabled="true"
                    :content-template="graphToolTipFunctionRecentScansMain"
                    :z-index="2000"
                  />
                  <DxCommonSeriesSettings
                    argument-field="StartDatetime"
                    type="line"
                    hover-mode="allArgumentPoints"
                  />
                  <DxArgumentAxis
                    argument-type="datetime"
                  >
                    <DxLabel
                      :staggering-spacing="10"
                      display-mode="stagger"
                    />
                  </DxArgumentAxis>
                  <DxSeries v-for="(val) in group.stat_columns" :key="val.stat_name"
                    :value-field="val.stat_name"
                    :name="val.stat_name_disp"
                  />
                  <DxLegend 
                    vertical-alignment="bottom"
                    horizontal-alignment="center"
                  >
                    <DxMargin :top="25"/>
                  </DxLegend>
                </DxChart>
              </div>
            </template>
          </DxTabPanel>
        </DxPopup>

      </div>
    </div>
  </div>
</template>

<script setup>
import { DxSelectBox } from 'devextreme-vue/select-box';
import { ref } from 'vue';
import databaseName from '../myFunctions/databaseName';
import { useRouter } from 'vue-router';
import auth from "../auth";
import notify from 'devextreme/ui/notify';
import { DxLoadPanel } from 'devextreme-vue/load-panel';
import axios from 'axios';
import DxButton from 'devextreme-vue/button';
import DxPopup from 'devextreme-vue/popup';
import DxTabPanel from 'devextreme-vue/tab-panel';
import { DxNumberBox } from 'devextreme-vue/number-box';
import DxDateBox from 'devextreme-vue/date-box';
import {
  DxDataGrid,
  DxColumn,
  DxScrolling,
  DxFilterRow,
  DxSummary,
  DxTotalItem,
  // DxMasterDetail,
  DxPager,
  DxPaging,
  // DxExport,
  DxSearchPanel,
} from 'devextreme-vue/data-grid';
import DxChart, {
    DxSeries,
    DxCommonSeriesSettings,
    DxTooltip,
    DxLabel,
    DxMargin,
    DxLegend,
    DxArgumentAxis,
} from 'devextreme-vue/chart';

//date type entension to format the dates into a string that we care about
Date.prototype.FormatDatetime = function() {
  let date = this.getFullYear()+'-'+((this.getMonth()+1).toString().padStart(2, '0'))+'-'+this.getDate().toString().padStart(2, '0');
  let time = this.getHours().toString().padStart(2, '0') + ":" + this.getMinutes().toString().padStart(2, '0') + ':' + this.getSeconds().toString().padStart(2, '0');
  return date + ' ' + time;
}


const router = useRouter();
const isMobile = ref(false);
isMobile.value = databaseName.getScreenSizeSmall();
const showMobileViewOrNotFunctionMainMonitor = () => {
  let viewConnectionPopupSearchParams = '';
  let viewConnectionPopupSingleSearchParams = '';
  let popupTimeRangeGridColumnHide = '';
  let popupGridTimeRangeColumnsWidth = '';
  if (isMobile.value) {
    viewConnectionPopupSearchParams = '';
    viewConnectionPopupSingleSearchParams = 'viewConnectionPopupSingleSearchParamsMobileMain';
    popupTimeRangeGridColumnHide = true;
    popupGridTimeRangeColumnsWidth = 'auto';
  } else {
    viewConnectionPopupSearchParams = 'viewConnectionPopupSearchParamsNormalMain';
    viewConnectionPopupSingleSearchParams = 'viewConnectionPopupSingleSearchParamsNormalMain';
    popupTimeRangeGridColumnHide = false;
    popupGridTimeRangeColumnsWidth = 170;
  }
  return {
    viewConnectionPopupSearchParams,
    viewConnectionPopupSingleSearchParams,
    popupTimeRangeGridColumnHide,
    popupGridTimeRangeColumnsWidth,
  }
};
const showMobileViewOrNotMainMonitor = showMobileViewOrNotFunctionMainMonitor();

const dataGridMain = ref(null);

const MainSiteData = ref([]);

const SiteName = ref("Sites");
const QueryHours = ref(4);

const loadingVisible = ref(false);

const showMainData = ref(true);

const selected_site_id = ref(0);

const siteConnectionsDS = ref([]);
const siteScannerInfoDS = ref([]);
const siteScannerColumnsDS = ref([]);

const popupSelectedConnectionid = ref(0);
const popupViewConnectionVisible = ref(false);
const popupViewConnectionDS = ref({});
const popupViewConnectionTitle = ref("");
const popupStartTimeString = ref("");
const popupStopTimeString = ref("");
const popupStopTime = ref(new Date());
const popupReturnDataInterval = ref(30);
const popupSelectionChanged = ref(0);
const popupGroupIndex = ref(0);

databaseName.isSomeoneLoggedIn().then(user => {
  console.log(user);
  if (user.data.sim_first_name == null) {
    let sessionStorageUser = sessionStorage.getItem("KOZ_SERVICE_AUTH");
    let sessionStorageJSON = JSON.parse(sessionStorageUser);

    if(sessionStorageJSON != null)
    {
      auth._simUser = sessionStorageJSON.data;
      if(databaseName.validateAuthLevel('create_modify'))
      {
        //put primer stuff in here for like functions to call to get data
        //'any' value passed to function will allow auth for anyuser
        GetMainSiteData();
      }
      else
      {
        notify('Your Account Does NOT Have Acces To This Page', 'warning', 10000);
        router.push('/home');
      }
    }
    else
    {
      notify('Please Log In To Use The Service Site', 'warning', 10000);
      router.push('/login-form');
    }
  }
  else
  {
    if(databaseName.validateAuthLevel('create_modify'))
    {
      GetMainSiteData();
    }
    else
    {
      notify('Your Account Does NOT Have Acces To This Page', 'warning', 10000);
      router.push('/home');
    }
  }

});

const filterDataGrid = () => {
  const dataGrid = dataGridMain.value.instance;
  if (statusSelected.value == 'monitor') {
    dataGrid.filter([
      ['koz_tracks_remote_stats', '=', true]
    ])
  } else if (statusSelected.value == 'nope') {
    dataGrid.filter([
      ['koz_tracks_remote_stats', '=', false]
    ])
  } else {
    dataGrid.clearFilter();
  }
  //getListOfErrorMessages();
};


const statusClearedSelectData = ref([
  { disp: "Show All", val: "all" },
  { disp: "Monitoring", val: "monitor" },
  { disp: "Not Monitoring", val: "nope" }
]);

const statusSelected = ref(statusClearedSelectData.value[0].val);

const graphToolTipFunctionRecentScansMain = (data) => {
  //console.log(data);
  //maybe its something with it being in a popup but tooltips are not working for some reason?
  return `${data.seriesName}: ${data.value}`;
};

const ShowSiteConnections = (site_id, site_name) => {
  selected_site_id.value = site_id;
  siteConnectionsDS.value = [];
  siteScannerInfoDS.value = [];
  siteScannerColumnsDS.value = [];
  showMainData.value = false;
  SiteName.value = site_name;

  GetSiteConnectionsData();
}

const GoBackToSitesClicked = () => {
  showMainData.value = true;
  SiteName.value = "Sites";
  GetMainSiteData();
}

const connectionNameClicked = (e) => {
  if(e.columnIndex == 0)
  {
    //console.log(e);
    popupSelectedConnectionid.value = e.key.idmonitor_warehouse_connections;

    popupViewConnectionTitle.value = `Connection: ${e.key.connection_name} Individual Stats`;
    popupReturnDataInterval.value = 30; 
    let tmpDate = new Date();
    popupStopTime.value = new Date();
    QueryHours.value = 4;
    tmpDate.setHours(tmpDate.getHours() - QueryHours.value);   
    popupStartTimeString.value = tmpDate.FormatDatetime();
    popupStopTimeString.value = new Date().FormatDatetime();
    popupGroupIndex.value = 0;

    getConnectionPopupData();
  }
}

const ReFilterButtonPressed = () => {
  let tmpDate = new Date(popupStopTime.value.FormatDatetime());
  //tmpDate = popupStopTime.value;
  tmpDate.setHours(tmpDate.getHours() - QueryHours.value);
  popupStartTimeString.value = tmpDate.FormatDatetime();

  popupStopTimeString.value = popupStopTime.value.FormatDatetime();

  getConnectionPopupData();
}

const getConnectionPopupData = () => {
  auth.getUser().then(user=>{
    loadingVisible.value = true;

    //max time span is 24 hours
    //intervals are 15, 30, and 60 minutes
    const Params = {
      Timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      StartDatetime: popupStartTimeString.value,
      EndDatetime: popupStopTimeString.value,
      IntervalMins: popupReturnDataInterval.value
    };

    axios({
      method: 'GET',
      url: `api/RemoteStats/OverallScreen/${selected_site_id.value}/${popupSelectedConnectionid.value}`,
      params: Params,
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`
      }
    })
    .then(resp=>{
      console.log(resp.data.data);

      for(let c = 0; c < resp.data.data.stat_groups.length; c++)
      {
        for(let i = 0; i < resp.data.data.stat_groups[c].TimeRanges.length; i++)
        {
          for(let tr = 0; tr < resp.data.data.stat_groups[c].stat_columns.length; tr++)
          {
            resp.data.data.stat_groups[c].TimeRanges[i][resp.data.data.stat_groups[c].stat_columns[tr].stat_name] = 0;
          }

          for(let sd = 0; sd < resp.data.data.stat_groups[c].TimeRanges[i].stat_names.length; sd++)
          {
            resp.data.data.stat_groups[c].TimeRanges[i][resp.data.data.stat_groups[c].TimeRanges[i].stat_names[sd].stat_name] = resp.data.data.stat_groups[c].TimeRanges[i].stat_names[sd].stat_count;
          }

        }
      }

      popupViewConnectionDS.value = resp.data.data;
      console.log(popupViewConnectionDS.value)

      popupViewConnectionVisible.value = true;
    })
    .catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify("No Data Found In Selected Time Frame", 'warning', 10000);
        popupViewConnectionDS.value = [];
        popupViewConnectionVisible.value = true;
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    })
    .finally(()=>{
      loadingVisible.value = false;
    })
  })
}


const getSiteStatusColor = (data) =>{
  if(data.data.koz_tracks_remote_stats)
  {
    if(data.data.koz_reported_problem)
    {
      return 'off-main';
    }
    else
    {
      return 'on-main';
    }
  }
  else
  {
    return 'na-main';
  }
}
const getConnectionStatusColorMain = (data) =>{
  if(data.data.warehouse_connection_heartbeat_in_range)
  {
    if(data.data.warehouse_connection_alive || data.data.warehouse_connection_type == "SERVICE")
    {
      return 'on-main';
    }
    else
    {
      return 'off-main';
    }
  }
  else
  {
    return 'off-main';
  }
}

const GetSiteConnectionsData = () => {
  auth.getUser().then(user=>{
    loadingVisible.value = true;

    axios({
      method: 'GET',
      url: `api/RemoteStats/OverallScreen/${selected_site_id.value}`,
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`
      }
    })
    .then(resp=>{
      console.log(resp.data.data);
      siteConnectionsDS.value = resp.data.data.WarehouseConnectionData;
      siteScannerColumnsDS.value = resp.data.data.ScannerColumns;

      let scannerColumns = {};

      //dynamically create all the data columns based on SCANNER information passed by API
      for(let c = 0; c < resp.data.data.ScannerColumns.length; c++)
      {
        scannerColumns[resp.data.data.ScannerColumns[c].stat_name] = 0;
      }

      let tmpScannerDataHolder = [];
      //dynamically create a list of all the scanners and add the columns from the above step to them
      for(let c = 0; c < resp.data.data.WarehouseConnectionData.length; c++)
      {
        if(resp.data.data.WarehouseConnectionData[c].warehouse_connection_type == 'SCANNER')
        {
          tmpScannerDataHolder.push(JSON.parse(JSON.stringify(scannerColumns)));
          tmpScannerDataHolder[tmpScannerDataHolder.length - 1].connection_name = resp.data.data.WarehouseConnectionData[c].connection_name;
          
        }
      }
      //dynamically adjust the counts for the scanners conditions
      for(let c = 0; c < resp.data.data.ScannerStats.length; c++)
      {
        if(resp.data.data.ScannerStats[c].connection_name == resp.data.data.ScannerStats[c].stat_group)
        {
          let tmpIndex = tmpScannerDataHolder.findIndex(p => p.connection_name == resp.data.data.ScannerStats[c].connection_name);
          if(tmpIndex != -1)
          {
            tmpScannerDataHolder[tmpIndex][resp.data.data.ScannerStats[c].stat_name] += resp.data.data.ScannerStats[c].sum;
          }
        }
      }

      siteScannerInfoDS.value = tmpScannerDataHolder;



    })
    .catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify(error.response.data.message, 'error', 10000);
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    })
    .finally(()=>{
      loadingVisible.value = false;
    })
  })
}

const GetMainSiteData = () => {
  auth.getUser().then(user=>{
    loadingVisible.value = true;

    axios({
      method: 'GET',
      url: `api/RemoteStats/OverallScreen`,
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`
      }
    })
    .then(resp=>{
      MainSiteData.value = resp.data.data;
    })
    .catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify(error.response.data.message, 'error', 10000);
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    })
    .finally(()=>{
      loadingVisible.value = false;
    })
  })
}
GetMainSiteData();

const logPopup = ref(false);
// const clickToOpenLogPopup = () => {
//   logPopup.value = !logPopup.value;
// };

const modifySearchPanelStyleLogsPopup = (e) => {
  if (e.parentType === "searchPanel") {
    e.editorOptions.stylingMode = "underlined";
  }
};
</script>

<style lang="scss" scoped>
.keyGroupContainerMain{
  display: flex;
  margin-top: 10px;
  align-items: center;
}
.groupContainerGridMain{
  display: flex;
  align-items: center;
}
.on-main{
  background-color: #90EE90;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 10px;
}
.off-main{
  background-color: #FF0000;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 10px;
}
.na-main{
  background-color: #4f4f4f;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 10px;
}
.scannerGridLinkMain {
  cursor: pointer;
  color: lightgray;
  text-decoration: underline;
}
.gridTotalsMain {
  color: #09b0d2!important;
  text-decoration: underline;
}
.viewConnectionPopupSearchParamsNormalMain {
  display: flex;
  align-items: flex-end;
  margin-bottom: 35px;
}
.viewConnectionPopupSingleSearchParamsNormalMain {
  margin-right: 25px;
}
.viewConnectionPopupSingleSearchParamsMobileMain {
  margin-bottom: 15px;
}
</style>
