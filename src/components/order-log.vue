<template>
	<DxLoadPanel :visible="isFakeLoading"  :show-pane="true" :container="'.tab-panel'" :position="{ of: '.tab-panel' }" />
  <div class="container" >
    <div class="row">
      <div>
        <label>Search LPN</label>
        <DxTextBox v-model:value='lpnInput' :dataSource='conditionTypes' placeholder="Enter LPN Here" styling-mode='contained' :onEnterKey="(e) => onSearchLPN()"  width="200px"/>
      </div>

      <div class="row" >
        <DxButton text="Search" @click="onSearchLPN" styling-mode="contained"  type="default" ></DxButton>

				<!-- <div>
					<div v-if="orderNumber"><span>Order Number:</span> <span class="accent">{{ orderNumber }}</span></div>
					<div v-if="totalCartons"><span>Total Cartons:</span> <span class="accent">{{ totalCartons }}</span></div>
				</div>

				<div>
					<div v-if="cartonWeight"><span>Carton Weight:</span> <span class="accent"> {{ cartonWeight }}</span></div>
					<div v-if="putwallLocation"><span>Put-Wall Location:</span> <span class="accent">{{ putwallLocation }}</span></div>
				</div> -->
      </div>
    </div>

		<div class="row"  style="height:calc(100vh - 450px)">

			<!-- {{ gridData }} -->
			<DxDataGrid
				:height="'100%'"
				:data-source="sortedTransactions"
				:row-alternation-enabled="true"
				:column-hiding-enabled="false"
				:show-borders="true"
				:word-wrap-enabled="true"
				:column-auto-width="true"
				:no-data-text="gridMsgError">

				<DxHeaderFilter :visible="true"/>
				<!-- <DxExport :enabled="true" /> -->
				<DxFilterPanel :visible="true"/>
				<DxScrolling mode="virtual" />

				<DxColumn data-field="transaction_datetime" caption="Date/Time"  cell-template="datetimeCellTemplate"  width="150"/>
				<DxColumn caption="Comms" cell-template="scannerPLCCellTemplate"  width="150"/>
				<DxColumn data-field="transaction_code" caption="Transaction" cell-template="actionCellTemplate" width="230"/>
				<DxColumn data-field="transaction_message" caption="Message" />


				<template #datetimeCellTemplate="{ data }">
					<div>
						{{ formatDateTime(data.data.transaction_datetime) }}
					</div>
				</template>

				<template #scannerPLCCellTemplate="{ data }">
					<div>
						{{ (data.data.scannerpoint || 'SYSTEM') + '/ ' + (data.data.plc || 'SYSTEM') }}
					</div>
				</template>

				<template #actionCellTemplate="{ data }">
					<div :style="{  backgroundColor: getTransactionCodeColor(data.data.transaction_code),  color: getTransactionTextColor(data.data.transaction_code),
            padding: '4px 8px', borderRadius: '10px',  width: 'fit-content', opacity: 0.85, textWrap: 'nowrap' }">
            {{ data.data.transaction_code }}
          </div>
				</template>
			</DxDataGrid>


		</div>
  </div>
</template>

<script setup>
  /*=====================================================================
    IMPORTS
  =====================================================================*/
  // Vue core
  import { ref, computed, onMounted, watch, nextTick, defineProps } from 'vue';

  // UI components & utilities
  import { DxButton } from 'devextreme-vue/button';
  import { DxTextBox } from 'devextreme-vue/text-box';
	import { DxLoadPanel } from 'devextreme-vue/load-panel';
	import { DxDataGrid, DxColumn, DxSearchPanel,DxScrolling, DxFilterPanel, DxExport, DxGroupPanel, DxHeaderFilter} from 'devextreme-vue/data-grid';
	import { exportDataGrid } from 'devextreme/excel_exporter';
	import { saveAs } from 'file-saver';
	import { Workbook } from 'exceljs';
	/*=====================================================================
    PROPS & EMITS
  =====================================================================*/
	const props = defineProps({
		fetchLPNTransactions: { type: Object},
		trans_log_lpn: { type: Array},
	})

	/*=====================================================================
    LIFECYCLE HOOKS
  =====================================================================*/
	onMounted(async () => {

	});

  /*=====================================================================
    REFS FOR LOCAL STATE
  =====================================================================*/
  const lpnInput = ref('');
	const transactionColorCache = ref({});
	const colorIndex = ref(0);
	const isFakeLoading = ref(false);
	const colorPalette = [
		"#B8D8FF", // Medium blue
    "#FFE066", // Medium yellow
    "#A8E6CF", // Medium green
    "#E0E0E0", // Medium gray
    "#FFB6B6", // Medium red
    "#B6B6FF", // Medium periwinkle
    "#FFD2B0", // Medium peach
    "#D2B0FF", // Medium purple
    "#B0D2FF", // Medium sky blue
    "#FFB0D8", // Medium pink
    "#FFE0B0", // Medium orange
    "#B0E0FF", // Medium aqua blue
    "#D8B0FF", // Medium lavender
    "#B0FFEE"  // Medium teal
	];

  /*=====================================================================
    COMPUTED PROPERTIES
  =====================================================================*/
 // Sort transactions by timestamp
	const sortedTransactions = computed(() => {
		// Check if trans_log_lpn is an array before trying to spread it
		if (!props.trans_log_lpn || !Array.isArray(props.trans_log_lpn)) {
			return [];
		}
		return [...props.trans_log_lpn].sort((a, b) =>
			new Date(a.transaction_datetime) - new Date(b.transaction_datetime)
		);
	});

  /*=====================================================================
    FUNCTIONS
  =====================================================================*/
  const onSearchLPN = () => {
		if (!lpnInput.value) return;
			
		isFakeLoading.value = true;
			setTimeout(() => {
				props.fetchLPNTransactions(lpnInput.value);
				isFakeLoading.value = false;
			}, 300);

  };

	const formatDateTime = (date) => {
		const d =  new Date(date);

		// Get year, month, day
		const year = d.getFullYear();
		const month = String(d.getMonth() + 1).padStart(2, '0');
		const day = String(d.getDate()).padStart(2, '0');

		// Get hours, minutes, seconds
		const hours = String(d.getHours()).padStart(2, '0');
		const minutes = String(d.getMinutes()).padStart(2, '0');
		const seconds = String(d.getSeconds()).padStart(2, '0');

		// Return the formatted string with a space between date and time
		return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
	}

	// Function to determine the background color based on transaction_code
	const getTransactionCodeColor = (transactionCode) => {
    // Check if we already have a color for this transaction code
    if (transactionColorCache.value[transactionCode]) {
      return transactionColorCache.value[transactionCode];
    }

    // Get the next color from our palette and store it in the cache
    const color = colorPalette[colorIndex.value % colorPalette.length];
    transactionColorCache.value[transactionCode] = color;
    colorIndex.value++; // Increment for the next unique code

    return color;
  };

	const getTextColorForBackground = (backgroundColor) => {
    // Convert hex to RGB
    const r = parseInt(backgroundColor.slice(1, 3), 16);
    const g = parseInt(backgroundColor.slice(3, 5), 16);
    const b = parseInt(backgroundColor.slice(5, 7), 16);

    // Calculate perceived brightness (YIQ formula)
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;

    // Return appropriate text color based on background brightness
    return brightness > 155 ? "#1F2937" : "#FFFFFF"; // Dark gray for light bg, white for dark bg
  };

	// Function to get text color that matches the background
	const getTransactionTextColor = (transactionCode) => {
    const bgColor = getTransactionCodeColor(transactionCode);
    return getTextColorForBackground(bgColor);
  };


	const onExporting = (e) => {
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Order Log');

    exportDataGrid({
        component: e.component,
        worksheet: worksheet,
        autoFilterEnabled: true,
    }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer) => {
            saveAs(new Blob([buffer], { type: 'application/octet-stream' }), 'DataGrid.xlsx');
        });
    });

    e.cancel = true;
	};


</script>

<style scoped lang="scss">
  @import "@/themes/generated/variables.additional.scss";

  .container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    flex: 1 1 auto;
    // overflow-y: auto;
    letter-spacing: 0.3px;
    border-radius: 8px;
    // border: 1px solid;
    // padding: 10px;
    // border-color: $base-border-color;
    background-color: rgba($base-bg-dark, .3);
    height: 100%;
    // margin: 20px 0;
		color: rgba($base-text-color, 0.75);
  }

  .row {
    display: flex;
    gap: 20px;
    flex-shrink: 0;
    align-items: end;
  }

	/* color classes */
	.accent {
		color: $base-accent;
	}

	.bg-accent {
		background-color: $base-accent;
	}

  /* DX Controls Override  */
  .dx-texteditor {
    border: 0px;
    min-width: 120px;
    border: 1px solid $base-border-color;
    border-radius: 5px;
    background-color: $base-bg-dark;
  }

  .dx-texteditor.dx-editor-filled.dx-state-disabled {
    background-color: $base-bg-dark;
    opacity: 1;
  }

  .dx-texteditor::before {
    content: none;
  }

  .dx-texteditor::after {
    content: none;
  }

  .dx-button {
    border-radius: 5px;
    box-shadow: none;
    height: 31px;
    margin-bottom: 1px;
    color: $base-text-color; //rgba($base-text-color, alpha($base-text-color)  * 0.85);
  }

  .custom-button.dx-button-has-icon {
    min-width: unset !important;
  }

  ::v-deep(.custom-button.dx-button-has-icon .dx-icon) {
    font-size: 8px !important;
    width: unset;
    height: unset;
  }

</style>