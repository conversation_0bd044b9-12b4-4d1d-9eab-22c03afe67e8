@import "./themes/generated/variables.additional.scss";
.content {
  line-height: 1.5;
  flex-grow: 1;
  display: flex;

  h2 {
    font-size: 30px;
    margin-top: 20px;
    margin-bottom: 20px;
  }
}

.container {
  height: 100%;
  flex-direction: column;
  display: flex;
}

.layout-body {
  flex: 1;
  min-height: 0;
}

.side-nav-outer-toolbar .dx-drawer {
  height: calc(100% - 56px)
}

.content-block {
  margin-left: 40px;
  margin-right: 40px;
  margin-bottom: 40px;

  .screen-x-small & {
    margin-left: 20px;
    margin-right: 20px;
  }
}

.responsive-paddings {
  padding: 20px;

  .screen-large & {
    padding: 40px;
  }
}
/* Scrollbar styling */
::-webkit-scrollbar {
  width: 12px; 
}

::-webkit-scrollbar-track {
  background: transparent;
  border: 4px solid transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba($base-text-color, 0.3);
  border-radius: 8px;
  border: 4px solid transparent; /* Creates padding effect */
  background-clip: padding-box;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba($base-text-color, 0.3);
}

.dx-card.wide-card {
  border-radius: 0;
  margin-left: 0;
  margin-right: 0;
  border-right: 0;
  border-left: 0;
}
.dx-card {
  border-radius: 12px;
}

.with-footer > .dx-scrollable-wrapper >
.dx-scrollable-container > .dx-scrollable-content {
  height: 100%;

  & > .dx-scrollview-content {
    display: flex;
    flex-direction: column;
    min-height: 100%;
  }
}

#app {
  height: 100%;
 }

$side-panel-min-width: 60px;
