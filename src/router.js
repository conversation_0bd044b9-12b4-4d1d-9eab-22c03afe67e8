import DashboardMonitor from './views/dashboard-mon';
import CreateTicket from './views/create-ticket';
import MainRemoteMonitor from './views/main-remote-monitor';
import ManageStats from './views/manage-stats';
import CustomerManager from './views/customer-manager';
import ViewTickets from './views/view-tickets';
import CreateTicketOriginal from './views/create-ticket-original';
import OrderDashboard from './views/order-dashboard';
import Diagnostics from './views/diagnostics-view';
import UserManager from './views/user-manager';
import DynamicDashboard from './views/dynamic-dashboard';
import auth from "./auth";
import { createRouter, createWebHashHistory } from "vue-router";

import Home from "./views/home-page";
import Profile from "./views/profile-page";
import defaultLayout from "./layouts/side-nav-outer-toolbar";
import simpleLayout from "./layouts/single-card";
import comingSoon from "./views/coming-soon";

function loadView(view) {
  return () => import (/* webpackChunkName: "login" */ `./views/${view}.vue`)
}

const router = new createRouter({
  routes: [
    {
      path: "/home",
      name: "home",
      meta: {
        title: 'Home',
        requiresAuth: true,
        layout: defaultLayout
      },
      component: Home
    },
    {
      path: "/profile",
      name: "profile",
      meta: {
        title: 'Profile',
        requiresAuth: true,
        layout: defaultLayout
      },
      component: Profile
    },
    {
      path: "/login-form",
      name: "login-form",
      meta: {
        requiresAuth: false,
        layout: simpleLayout,
        title: "Sign In"
      },
      component: loadView("login-form")
    },
    {
      path: "/dynamic-dashboard",
      name: "dynamic-dashboard",
      meta: {
        requiresAuth: true,
        layout: defaultLayout,
        title: 'Dashboard',
        userSecurity: ['administrator', 'programmer'],
      },
      component: DynamicDashboard
    },
    // {
    //   path: "/reset-password",
    //   name: "reset-password",
    //   meta: {
    //     requiresAuth: false,
    //     layout: simpleLayout,
    //     title: "Reset Password",
    //     description: "Please enter the email address that you used to register, and we will send you a link to reset your password via Email."
    //   },
    //   component: loadView("reset-password-form")
    // },
    // {
    //   path: "/create-account",
    //   name: "create-account",
    //   meta: {
    //     requiresAuth: false,
    //     layout: simpleLayout,
    //     title: "Sign Up"
    //   },
    //   component: loadView("create-account-form"),
    // },
    // {
    //   path: "/change-password/:recoveryCode",
    //   name: "change-password",
    //   meta: {
    //     requiresAuth: false,
    //     layout: simpleLayout,
    //     title: "Change Password"
    //   },
    //   component: loadView("change-password-form")
    // },
    {
      path: "/",
      redirect: "/login-form"
    },
    {
      path: "/recovery",
      redirect: "/home"
    },
    {
      path: "/:pathMatch(.*)*",
      redirect: "/home"
    }, 
    {
      path: "/user-manager",
      name: "user-manager",
      meta: {
        requiresAuth: true,
        layout: defaultLayout
      },
      component: UserManager
    }, 
    {
      path: "/create-ticket-original",
      name: "create-ticket-original",
      meta: {
        requiresAuth: true,
        layout: defaultLayout
      },
      component: CreateTicketOriginal
    }, 
    {
      path: "/view-tickets",
      name: "view-tickets",
      meta: {
        title: 'View Tickets',
        requiresAuth: true,
        layout: defaultLayout
      },
      component: ViewTickets
    }, 
    {
      path: "/customer-manager",
      name: "customer-manager",
      meta: {
        title: 'Customer Manager',
        requiresAuth: true,
        layout: defaultLayout
      },
      component: CustomerManager
    }, 
    {
      path: "/manage-stats",
      name: "manage-stats",
      meta: {
        title: 'Connection/Stat Manager',
        requiresAuth: true,
        layout: defaultLayout
      },
      component: ManageStats
    }, 
    {
      path: "/main-remote-monitor",
      name: "main-remote-monitor",
      meta: {
        title: 'Remote Monitor',
        requiresAuth: true,
        layout: defaultLayout
      },
      component: MainRemoteMonitor
    }, 
    {
      path: "/create-ticket",
      name: "create-ticket",
      meta: {
        title: 'Create Ticket',
        requiresAuth: true,
        layout: defaultLayout
      },
      component: CreateTicket
    }, 
    {
      path: "/dashboard-monitor",
      name: "dashboard-monitor",
      meta: {
        title: 'Remote Monitor',
        requiresAuth: true,
        layout: defaultLayout
      },
      component: DashboardMonitor
    },
    {
      path: "/order-dashboard",
      name: "order-dashboard",
      meta: {
        requiresAuth: true,
        userSecurity: ['administrator', 'programmer'],
        layout: defaultLayout,
        title: 'Order Dashboard'
      },
      component: OrderDashboard
    },
    {
      path: "/diagnostics-view",
      name: "diagnostics",
      meta: {
        requiresAuth: true,
        userSecurity: ['administrator', 'programmer'],
        layout: defaultLayout,
        title: 'Logs & Diagnostics'
      },
      component: Diagnostics
    },
    {
      path: "/coming-soon",
      name: "coming-soon",
      meta: {
        requiresAuth: true,
        layout: defaultLayout,
        title: 'Stat Manager'
      },
      component: comingSoon
    },
    
  ],
  history: createWebHashHistory()
});

router.beforeEach((to, from, next) => {

  // if (to.name === "login-form" && auth.loggedIn()) {
  //   next({ name: "home" });
  // }

  if (to.matched.some(record => record.meta.requiresAuth)) {
    if (!auth.loggedIn()) {
      next({
        name: "login-form",
        query: { redirect: to.fullPath }
      });
    } else {
      next();
    }
  } else {
    next();
  }
});

export default router;
