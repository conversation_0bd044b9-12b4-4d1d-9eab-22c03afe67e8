export function useLogger(prefix, isEnabled = true) {
  const log = (...args) => {
    if (isEnabled) {
      console.log(`[${prefix}]`, ...args)
    }
  }

  const warn = (...args) => {
    if (isEnabled) {
      console.warn(`[${prefix}]`, ...args)
    }
  }

  const error = (...args) => {
    if (isEnabled) {
      console.error(`[${prefix}]`, ...args)
    }
  }

  const info = (...args) => {
    if (isEnabled) {
      console.info(`[${prefix}]`, ...args)
    }
  }

  return {
    log,
    warn,
    error,
    info
  }
}