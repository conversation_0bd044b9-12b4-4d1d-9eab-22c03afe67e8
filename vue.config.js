module.exports = {
  publicPath: "/SRS-WEB"
};

//this is used for the php api i made
//have the api using port 8000 and the aplication using port 8080 need to change later
//target: 'http://localhost:63948',
//target: 'http://kvkprd.monatglobal.com',
module.exports = {
  devServer: {
    //local host
    // proxy: {
    //   '^/api/':{
    //     target: 'http://localhost:63948',
    //     changeOrigin: true,
    //   }
    // }
    //KOZ Server
    // proxy: {
    //   '^/api/':{
    //     target: 'http://koz-software.com:8080/',
    //     changeOrigin: true,
    //   }
    // }
    // UDS KOZ SERVER TEST
    proxy: {
      '^/api/':{
        target: 'https://service.koz-software.com/',
        changeOrigin: true,
      }
    }
  }
}
