
export function useFormatHelper() {
	const formatValue = (value, dataType, format) => {

    
    // Handle different data types
    switch (dataType) {
      case 'currency':
        return new Intl.NumberFormat(format.locale || 'en-US', { 
          style: 'currency', 
          currency: format.currency || 'USD',
          minimumFractionDigits: format.minimumFractionDigits !== undefined ? format.minimumFractionDigits : 2,
          maximumFractionDigits: format.maximumFractionDigits !== undefined ? format.maximumFractionDigits : 2
        }).format(value);
        
      case 'percent':
        return new Intl.NumberFormat(format.locale || 'en-US', { 
          style: 'percent',
          minimumFractionDigits: format.minimumFractionDigits !== undefined ? format.minimumFractionDigits : 1,
          maximumFractionDigits: format.maximumFractionDigits !== undefined ? format.maximumFractionDigits : 1
        }).format(format.divideBy100 !== false ? value / 100 : value);
        
      case 'decimal':
        return new Intl.NumberFormat(format.locale || 'en-US', {
          minimumFractionDigits: format.minimumFractionDigits !== undefined ? format.minimumFractionDigits : 2,
          maximumFractionDigits: format.maximumFractionDigits !== undefined ? format.maximumFractionDigits : 2
        }).format(value);
      
      case 'integer':
        return new Intl.NumberFormat(format.locale || 'en-US', {
          maximumFractionDigits: 0
        }).format(value);
        
      case 'date':
        try {
          const dateValue = new Date(value);
          return new Intl.DateTimeFormat(
            format.locale || 'en-US', 
            format.dateOptions || { dateStyle: 'medium' }
          ).format(dateValue);
        } catch (e) {
          console.error('Error formatting date:', e);
          return value;
        }
        
      case 'text':
        // Return as is, or apply any text formatting needed
        return String(value);
        
      case 'boolean':
        // Convert boolean to text (can be customized)
        return value ? (format.trueText || 'Yes') : (format.falseText || 'No');
        
      case 'number':
      default:
        // Default number formatting
        return new Intl.NumberFormat(format.locale || 'en-US', {
          minimumFractionDigits: format.minimumFractionDigits || 0,
          maximumFractionDigits: format.maximumFractionDigits || 0
        }).format(value);
    }
  };

  return {
    formatValue
  }
}