import { ref, onMounted, onUnmounted } from 'vue'

export function useScreenSize() {
  const windowWidth = ref(window.innerWidth)
  const isMobile = ref(windowWidth.value < 768) // Common breakpoint for mobile
  
  function updateWidth() {
    windowWidth.value = window.innerWidth
    isMobile.value = windowWidth.value < 768
  }
  
  onMounted(() => {
    window.addEventListener('resize', updateWidth)
  })
  
  onUnmounted(() => {
    window.removeEventListener('resize', updateWidth)
  })
  
  return {
    windowWidth,
    isMobile
  }
}