import axios from 'axios';
import { ref } from 'vue';
import auth from '../auth';

function arrClone(source){
    return JSON.parse(JSON.stringify(source));
}
//these are paths to the php api, make sure to keep the naming shceme the same only change the file name
//ex /api/CLAIRES-AUTH-API.php?loginMethod=  becomes /api/MONAT-AUTH-API.php?loginMethod=
// var tmpJson = {auth: "/apiphp/KEHE-AUTH-API.php?loginMethod=", get: "/apiphp/KEHE-GET-API.php?command=", update: '/apiphp/KEHE-UPDATE-API.php', general: '/api/KOZData/'};
// hi
var websiteVersion = '1.0.8'; //production
//var websiteVersion = '3.0.5'; //test

var numberOfDays = '10';

//this var is for the amount of scanners keep as int
var AmtOfScanners = 2;

var CryptoJS = require('crypto-js');
const KEY = "Yp2s5v8y/B?E(H+MbQeThWmZq4t6w9z$";

var securityTypes = {
    admin:[
        "ADMIN"
    ],
    customer_modify:[
        "ADMIN",
        "SERVICE_ADMIN"
    ],
    create_modify:[
        "ADMIN",
        "SERVICE",
        "SERVICE_ADMIN"
    ],
    stats_modify:[
        "ADMIN"
    ]
}

//name == the NAME of the stat group in KOZ
//caption == the name that will be displayed to customer
//STATUSES PER displayGroups MUST BE UNIQUE!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
//you can for example have duplicates across StatGroupData elements
var StatGroupData = [
    {
        name: "Import",
        caption: "Import Data",
        displayGroups:[
            {
                displayName: "Import Data",
                statuses:[
                    {name: "Success", caption: "Success"},
                    {name: "Error", caption: "Failure"}
                ],
                data:[]
            }
        ]
        
    },
    {
        name: "DivConfirm",
        caption: "Responses From Conveyor",
        displayGroups:[
            {
                displayName: "Freezer-Picking",
                statuses:[
                    {name: "A", caption: "Zone A"},
                    {name: "B", caption: "Zone B"},
                    {name: "C", caption: "Zone C"},
                    {name: "D", caption: "Zone D"},
                    {name: "E", caption: "Zone E"},
                    {name: "F", caption: "Zone F"},
                    {name: "G", caption: "Zone G"},
                    {name: "H", caption: "Zone H"},
                    {name: "J", caption: "Zone J"}
                ],
                data:[]
            },
            {
                displayName: "Ambient-Picking",
                statuses:[
                    {name: "K", caption: "Zone K"},
                    {name: "L", caption: "Zone L"},
                    {name: "M", caption: "Zone M"},
                    {name: "N", caption: "Zone N"},
                    {name: "P", caption: "Zone P"},
                    {name: "Q", caption: "Zone Q"},
                    {name: "R", caption: "Zone R"},
                    {name: "S", caption: "Zone S"},
                    {name: "T", caption: "Zone T"},
                    {name: "U", caption: "Zone U"},
                    {name: "V", caption: "Zone V"},
                    {name: "X", caption: "Zone X"},
                    {name: "W", caption: "Zone W"},
                    {name: "Y", caption: "Zone Y"},
                    {name: "Z", caption: "Zone Z"}
                ],
                data:[]
            },
            {
                displayName: "Packing",
                statuses:[
                    {name: "BYPASS", caption: "Bypass Pick02"},
                    {name: "PACK01", caption: "Pack Lane 01"},
                    {name: "PACK02", caption: "Pack Lane 02"},
                    {name: "PACK03", caption: "Pack Lane 03"},
                    {name: "PACK04", caption: "Pack Lane 04"},
                    {name: "PACK05", caption: "Pack Lane 05"},
                    {name: "PACK06", caption: "Pack Lane 06"},
                    {name: "PACK07", caption: "Pack Lane 07"},
                    {name: "PACK08", caption: "Pack Lane 08"},
                    {name: "PANDA01", caption: "PANDA 01"},
                    {name: "PANDA02", caption: "PANDA 02"},
                    {name: "PANDA03", caption: "PANDA 03"},
                    {name: "PANDA04", caption: "PANDA HOSPITAL"}
                ],
                data:[]
            },
            {
                displayName: "Shipping",
                statuses:[
                    {name: "LANE01", caption: "Ship Lane 01"},
                    {name: "LANE02", caption: "Ship Lane 02"},
                    {name: "LANE03", caption: "Ship Lane 03"},
                    {name: "LANE04", caption: "Ship Lane 04"},
                    {name: "LANE05", caption: "Ship Lane 05"},
                    {name: "LANE06", caption: "Ship Lane 06"},
                    {name: "LANE07", caption: "Ship Lane 07"},
                    {name: "LANE08", caption: "Ship Lane 08"},
                    {name: "LANE09", caption: "Ship Lane Hospital"}
                ],
                data:[]
            }
        ]
        
    },
    {
        name: "DivRequest",
        caption: "KOZ Requests/Activity",
        displayGroups:[
            {
                displayName: "Freezer-Picking",
                statuses:[
                    {name: "A", caption: "Zone A"},
                    {name: "B", caption: "Zone B"},
                    {name: "C", caption: "Zone C"},
                    {name: "D", caption: "Zone D"},
                    {name: "E", caption: "Zone E"},
                    {name: "F", caption: "Zone F"},
                    {name: "G", caption: "Zone G"},
                    {name: "H", caption: "Zone H"},
                    {name: "J", caption: "Zone J"}
                ],
                data:[]
            },
            {
                displayName: "Ambient-Picking",
                statuses:[
                    {name: "BYPASS", caption: "Bypass Pick02"},
                    {name: "K", caption: "Zone K"},
                    {name: "L", caption: "Zone L"},
                    {name: "M", caption: "Zone M"},
                    {name: "N", caption: "Zone N"},
                    {name: "P", caption: "Zone P"},
                    {name: "Q", caption: "Zone Q"},
                    {name: "R", caption: "Zone R"},
                    {name: "S", caption: "Zone S"},
                    {name: "T", caption: "Zone T"},
                    {name: "U", caption: "Zone U"},
                    {name: "V", caption: "Zone V"},
                    {name: "X", caption: "Zone X"},
                    {name: "W", caption: "Zone W"},
                    {name: "Y", caption: "Zone Y"},
                    {name: "Z", caption: "Zone Z"}
                ],
                data:[]
            },
            {
                displayName: "Packing",
                statuses:[
                    {name: "PACKSORTER", caption: "Pack Sorter"},
                    {name: "SCALE01", caption: "Valid Scale Weight"},
                    {name: "NOWEIGHT", caption: "Missing Scale Weight"},
                    {name: "PANDASORTER", caption: "PANDA Sorter"},
                    {name: "PANDA01", caption: "PANDA 01"},
                    {name: "PANDA02", caption: "PANDA 02"},
                    {name: "PANDA03", caption: "PANDA 03"},
                    {name: "PANDA04", caption: "PANDA HOSPITAL"},
                    {name: "SHIPVALID", caption: "Track Num Validated"},
                    {name: "MISSMATCH", caption: "Track Num Missmatch"}
                ],
                data:[]
            },
            {
                displayName: "Shipping",
                statuses:[
                    {name: "AMAZON", caption: "AMAZON"},
                    {name: "UPS", caption: "UPS"},
                    {name: "USPS", caption: "USPS"},
                    {name: "FEDEX", caption: "FEDEX"},
                    {name: "NOTMAPPED", caption: "Carrier Not Mapped"},
                    {name: "ERRORLABEL", caption: "Error Label Detected"}
                ],
                data:[]
            },
            {
                displayName: "General-Issues",
                statuses:[
                    {name: "NoRead", caption: "No Read"},
                    {name: "NoData", caption: "No Data"},
                    {name: "ReadConflict", caption: "Read Conflict"},
                    {name: "Critical", caption: "Critical Error"}
                ]
            }
        ]
    }
]

var ActiveScannersData = {
    DefaultScannerStatus:[
        {name: "CommsUp", caption: "Comms Up"},
        {name: "CommsDown", caption: "Comms Down"},
        {name: "Success", caption: "Success"},
        {name: "NoRead", caption: "No Read"},
        {name: "NoData", caption: "No Data"},
        {name: "ReadConflict", caption: "Read Conflict"},
        {name: "Critical", caption: "Critical"}
    ],
    ScannerGroups:[
        {
            groupName: "Freezer-Picking",
            chartDisplayIndex: 0,
            scannersInGroup:[
                {
                    scannerName: "SCAN01",
                    enabled: true,
                    data: []
                },
                {
                    scannerName: "SCAN02",
                    enabled: true,
                    data: []
                },
                {
                    scannerName: "SCAN03",
                    enabled: true,
                    data: []
                },
                {
                    scannerName: "SCAN04",
                    enabled: true,
                    data: []
                },
                {
                    scannerName: "SCAN05",
                    enabled: true,
                    data: []
                }
            ],
            extraStatuses: [
            ],
            data: []
        },
        {
            groupName: "Ambient-Picking",
            chartDisplayIndex: 0,
            scannersInGroup:[
                {
                    scannerName: "SCAN06",
                    enabled: true,
                    data: []
                },
                {
                    scannerName: "SCAN08",
                    enabled: true,
                    data: []
                },
                {
                    scannerName: "SCAN09",
                    enabled: true,
                    data: []
                },
                {
                    scannerName: "SCAN10",
                    enabled: true,
                    data: []
                },
                {
                    scannerName: "SCAN11",
                    enabled: true,
                    data: []
                },
                {
                    scannerName: "SCAN12",
                    enabled: true,
                    data: []
                },
                {
                    scannerName: "SCAN13",
                    enabled: true,
                    data: []
                },
                {
                    scannerName: "SCAN14",
                    enabled: true,
                    data: []
                }
            ],
            extraStatuses: [],
            data: []
        },
        {
            groupName: "Packing",
            chartDisplayIndex: 0,
            scannersInGroup:[
                {
                    scannerName: "SCAN15",
                    enabled: true,
                    data: []
                },
                {
                    scannerName: "SCAN17",
                    enabled: true,
                    data: []
                },
                {
                    scannerName: "SCAN18",
                    enabled: true,
                    data: []
                },
                {
                    scannerName: "SCAN19",
                    enabled: true,
                    data: []
                },
                {
                    scannerName: "SCAN20",
                    enabled: true,
                    data: []
                },
                {
                    scannerName: "SCAN21",
                    enabled: true,
                    data: []
                },
                {
                    scannerName: "SCAN22",
                    enabled: true,
                    data: []
                },
                {
                    scannerName: "SCAN23",
                    enabled: true,
                    data: []
                },
                {
                    scannerName: "SCAN24",
                    enabled: true,
                    data: []
                },
                {
                    scannerName: "SCAN25",
                    enabled: true,
                    data: []
                },
                {
                    scannerName: "SCAN26",
                    enabled: true,
                    data: []
                },
                {
                    scannerName: "SCAN27",
                    enabled: true,
                    data: []
                }
            ],
            extraStatuses: [],
            data: []
        },
        {
            groupName: "Shipping",
            chartDisplayIndex: 0,
            scannersInGroup:[
                {
                    scannerName: "SCAN28",
                    enabled: true,
                    data: []
                },
                {
                    scannerName: "SCAN29",
                    enabled: true,
                    data: []
                }
            ],
            extraStatuses: [],
            data: []
        }
    ]
}

//this is where you put the divert locations used for production counts
//bays refers to another location like aisle and shelf location
//even if if is not bays keep naming schema bays just used for display purposes
var prodJson = [
    {divert: '11', bays: 'Lane 11'},
    {divert: '12', bays: 'Lane 12'},
    {divert: '13', bays: 'Lane 13'},
    {divert: '14', bays: 'Lane 14'},
    {divert: '15', bays: 'Lane 15'},
    {divert: '16', bays: 'Lane 16'},
    {divert: '17', bays: 'Lane 17'},
    {divert: '18', bays: 'Lane 18'},
    {divert: '19', bays: 'Lane 19'},
    {divert: '21', bays: 'Lane 21'},
    {divert: '22', bays: 'Lane 22'},
    {divert: '23', bays: 'Lane 23'},
    {divert: '24', bays: 'Lane 24'},
    {divert: '25', bays: 'Lane 25'},
    {divert: '26', bays: 'Lane 26'},
    {divert: '27', bays: 'Lane 27'},
    {divert: '28', bays: 'Lane 28'},
    {divert: '29', bays: 'Lane 29'}
];

var internalDivertMap = {
    divZone1: "Picking Lane 1",
    divZone2: "Picking Lane 2",
    divZone3: "Picking Lane 3",
    divZone4: "Picking Lane 4",
    divZone5: "Picking Lane 5",
    divZone6: "remove",
    divZone7: "Spiral Check 1",
    divZone8: "Spiral Check 2",
    divZone9: "Dangerous Goods",
    divZone10: "Packing Lane 1",
    divZone11: "Packing Lane 2",
    divZone12: "Packing Lane 3",
    shipZone1: "Shipping Lane",
    shipZone2: "Shipping PGI",
    shipZone3: "Force Hospital",
    shipZone4: "remove",
    statZone1: "Weight Tolerance",
    statZone2: "XPS Label",
    statZone3: "Tracking Number Match",
    statZone4: "SAP Tracking Confirm",
    block1Title: "Picking",
    block2Title: "Picking / Spiral",
    block3Title: "Packing",
    block4Title: "Shipping",
    block5Title: "Packing Status"
};

var internalDivertMapReferenceObject = {
    divZone1: "LANE01",
    divZone2: "LANE02",
    divZone3: "LANE03",
    divZone4: "LANE04",
    divZone5: "LANE05",
    divZone6: "remove",
    divZone7: "SCAN06",
    divZone7Secondary: "SCANNER06",
    //above is picking
    divZone8: "SCAN07",
    divZone8Secondary: "SCANNER07",
    divZone9: "DGFLAG",
    divZone10: "PACKINGLANE01",
    divZone10Secondary: "SCANNER09",
    divZone10Complete: "SCANNER12",
    divZone11: "PACKINGLANE02",
    divZone11Secondary: "SCANNER13",
    divZone11Complete: "SCANNER16",
    divZone12: "PACKINGLANE03",
    divZone12Secondary: "SCANNER17",
    divZone12Complete: "SCANNER20",
    //above is packing
    divZoneShipIssue: "SCANNER22HOSPITAL",
    divZoneShipSuccess1: "PARCEL.HOSPITAL",
    divZoneShipSuccess2: "PARCEL.SHIP.02",
    divZoneShipSuccess3: "PARCEL.SHIP.03",
    divZoneShipSuccess4: "PARCEL.SHIP.04",
    shipZone1: "Ship",
    shipZone2Success: "PGISUCCESS",
    shipZone2Issue: "PGIERROR",
    shipZone3: "pgi",
    shipZone4: "remove",
    //above is shipping
    statZone1Success: "WEIGHTPASS",
    statZone1Issue: "WEIGHTFAIL",
    statZone2: "XPS Label",
    statZone2Success1: "YESINITRATE",
    statZone2Success2: "YESRERATE",
    statZone2Issue1: "ERRORINITRATE",
    statZone2Issue2: "ERRORRERATE",
    statZone2Dup: "DUPLICATERERATE",
    statZone3Success: "TRACKMATCH",
    statZone3Issue: "TRACKMISMATCH",
    statZone4Success1: "TRACKLATEST",
    statZone4Success2: "TRACKNOTLATEST",
    statZone4Issue: "ERRORRTRACK",
    //above is packing
    block1Title: "Picking",
    block2Title: "Picking / Spiral",
    block3Title: "Packing",
    block4Title: "Shipping",
    block5Title: "Packing Status"
}

var pickingMonitorMap = [
    {scanner: "SCAN06", conveyorArea: "hospitalLane"},
    {scanner: "SCAN06", conveyorArea: "packingLane"},
    {scanner: "SCAN02", conveyorArea: "northCortex"},
    {scanner: "SCAN04", conveyorArea: "southCortex"},
    {scanner: "SCAN01", conveyorArea: "orderStart"}
];

var shippingMonitorMap = [
    {scanner: "SCAN07", conveyorArea: "shippingWest"},
    {scanner: "SCAN08", conveyorArea: "shippingWest"},
    {scanner: "SCAN09", conveyorArea: "shippingEast"}
];

var scannerZones = {
    scanZone1: "SCAN00",
    scanZone2: "SCAN00",
    scanZone3: "SCAN00",
    scanZone4: "SCAN00",
    scanZone5: "SCAN00",
    scanZone6: "SCAN00",
    shippingScanner1: "SCAN21",
    shippingScanner2: "SCAN22"
};

var osMonitorMap = [
    {scanner: "SCAN01", conveyorArea: "osLane01"},
    {scanner: "SCAN02", conveyorArea: "osLane02"},
    {scanner: "SCAN03", conveyorArea: "osLane03"},
    {scanner: "SCAN04", conveyorArea: "osLane04"},
    {scanner: "SCAN05", conveyorArea: "pickInfeed"},
    {scanner: "SCAN06", conveyorArea: "obPick02"},
    {scanner: "SCAN07", conveyorArea: "obPick01"},
    {scanner: "SCAN06", conveyorArea: "pickSpiral"},
    {scanner: "SCAN07", conveyorArea: "pickSpiral"}
]

var packMonitorMap = [
    {scanner: "SCAN08", conveyorArea: "packInfeed"},
    {scanner: "SCAN09", conveyorArea: "packScale01"},
    {scanner: "SCAN09", conveyorArea: "packLane01Conveyor"},
    {scanner: "SCAN10", conveyorArea: "hazPanda07"},
    {scanner: "SCAN11", conveyorArea: "shpPanda08"},
    {scanner: "SCAN12", conveyorArea: "shpPanda08"},
    {scanner: "SCAN13", conveyorArea: "packScale02"},
    {scanner: "SCAN13", conveyorArea: "packLane02Conveyor"},
    {scanner: "SCAN14", conveyorArea: "hazPanda09"},
    {scanner: "SCAN15", conveyorArea: "shpPanda10"},
    {scanner: "SCAN16", conveyorArea: "shpPanda10"},
    {scanner: "SCAN17", conveyorArea: "packScale03"},
    {scanner: "SCAN17", conveyorArea: "packLane03Conveyor"},
    {scanner: "SCAN18", conveyorArea: "hazPanda11"},
    {scanner: "SCAN19", conveyorArea: "shpPanda12"},
    {scanner: "SCAN20", conveyorArea: "shpPanda12"},
    {scanner: "SCAN21", conveyorArea: "shpSorterLane"},
    {scanner: "SCAN22", conveyorArea: "shpSorterLane"}
    
]

var packMonitorNames = {
    packLane01Conveyor:{
        displayName: "Conveyor",
        lookupName: "packLane01Conveyor"
    },
    packLane02Conveyor:{
        displayName: "Conveyor",
        lookupName: "packLane02Conveyor"
    },
    packLane03Conveyor:{
        displayName: "Conveyor",
        lookupName: "packLane03Conveyor"
    },
    packLane01:{
        displayName: "Packing Lane 1",
        lookupName: "packLane01"
    },
    packLane02:{
        displayName: "Packing Lane 2",
        lookupName: "packLane02"
    },
    packLane03:{
        displayName: "Packing Lane 3",
        lookupName: "packLane03"
    },
    packInfeed:{
        displayName: "From Pick To Pack",
        lookupName: "packInfeed"
    },
    packScale01:{
        displayName: "Scale 1",
        lookupName: "packScale01"
    },
    packScale02:{
        displayName: "Scale 2",
        lookupName: "packScale02"
    },
    packScale03:{
        displayName: "Scale 3",
        lookupName: "packScale03"
    },
    hazPanda07:{
        displayName: "Panda 7 HAZ",
        lookupName: "hazPanda07"
    },
    hazPanda09:{
        displayName: "Panda 9 HAZ",
        lookupName: "hazPanda09"
    },
    hazPanda11:{
        displayName: "Panda 11 HAZ",
        lookupName: "hazPanda11"
    },
    shpPanda08:{
        displayName: "Panda 8 SHP",
        lookupName: "shpPanda08"
    },
    shpPanda10:{
        displayName: "Panda 10 SHP",
        lookupName: "shpPanda10"
    },
    shpPanda12:{
        displayName: "Panda 12 SHP",
        lookupName: "shpPanda12"
    },
    shpSorterLane:{
        displayName: "Shipping Sorter Lane + Recirc Loop",
        lookupName: "shpSorterLoop"
    },
    shpLane01:{
        displayName: "Ship Lane 2",
        lookupName: "shpLane02"
    },
    shpSpiral01:{
        displayName: "SP 2",
        lookupName: "shpSpiral02"
    },
    shpLane02:{
        displayName: "Ship Lane 1 (HOSPITAL)",
        lookupName: "shpLane01"
    },
    shpSpiral02:{
        displayName: "SP 1",
        lookupName: "shpSpiral01"
    },
    shpLane03:{
        displayName: "Ship Lane 3",
        lookupName: "shpLane03"
    },
    shpSpiral03:{
        displayName: "SP 3",
        lookupName: "shpSpiral03"
    },
    shpLane04:{
        displayName: "Ship Lane 4",
        lookupName: "shpLane04"
    },
    shpSpiral04:{
        displayName: "SP 4",
        lookupName: "shpSpiral04"
    }
}

var osMonitorNames = {
    orderStartToPick:{
        displayName: "Order Start To Picking Lane",
        lookupName: "pickInfeed"
    },
    orderStart1:{
        displayName: "Order Start 1 (A)",
        lookupName: "osLane01"
    },
    orderStart2:{
        displayName: "Order Start 2 (B)",
        lookupName: "osLane02"
    },
    orderStart3:{
        displayName: "Order Start 3 (C)",
        lookupName: "osLane03"
    },
    orderStart4:{
        displayName: "Order Start 4 (D)",
        lookupName: "osLane04"
    },
    pickLane1:{
        displayName: "Pick Lane 1",
        lookupName: "pickLane01"
    },
    pickLane2:{
        displayName: "Pick Lane 2",
        lookupName: "pickLane02"
    },
    pickLane3:{
        displayName: "Pick Lane 3",
        lookupName: "pickLane03"
    },
    pickLane4:{
        displayName: "Pick Lane 4",
        lookupName: "pickLane04"
    },
    pickLane5:{
        displayName: "Pick Lane 5",
        lookupName: "pickLane05"
    },
    pickToSpiral1to3:{
        displayName: "Pick Lanes 1-3 To Spiral",
        lookupName: "obPick01"
    },
    pickToSpiral4to5:{
        displayName: "Pick Lanes 4-5 To Spiral",
        lookupName: "obPick02"
    },
    pickSpiral:{
        displayName: "Spiral",
        lookupName: "pickSpiral"
    }
}



var scannerNames = [
    {mysqlName: "SCAN01", intValue: 1, displayName: "OS 1"},
    {mysqlName: "SCAN02", intValue: 2, displayName: "OS 2"},
    {mysqlName: "SCAN03", intValue: 3, displayName: "OS 3"},
    {mysqlName: "SCAN04", intValue: 4, displayName: "OS 4"},
    {mysqlName: "SCAN05", intValue: 5, displayName: "Pick Sorter"},
    {mysqlName: "SCAN06", intValue: 6, displayName: "Spiral 01"},
    {mysqlName: "SCAN07", intValue: 7, displayName: "Spiral 02"},
    {mysqlName: "SCAN08", intValue: 8, displayName: "Pack Sorter"},
    {mysqlName: "SCAN09", intValue: 9, displayName: "Pack 01 Scale/HAZ"},
    {mysqlName: "SCAN10", intValue: 10, displayName: "Pack 01 PANDA"},
    {mysqlName: "SCAN11", intValue: 11, displayName: "Pack 01 HU"},
    {mysqlName: "SCAN12", intValue: 12, displayName: "Pack 01 Track"},
    {mysqlName: "SCAN13", intValue: 13, displayName: "Pack 02 Scale/HAZ"},
    {mysqlName: "SCAN14", intValue: 14, displayName: "Pack 02 PANDA"},
    {mysqlName: "SCAN15", intValue: 15, displayName: "Pack 02 HU"},
    {mysqlName: "SCAN16", intValue: 16, displayName: "Pack 02 Track"},
    {mysqlName: "SCAN17", intValue: 17, displayName: "Pack 03 Scale/HAZ"},
    {mysqlName: "SCAN18", intValue: 18, displayName: "Pack 03 PANDA"},
    {mysqlName: "SCAN19", intValue: 19, displayName: "Pack 03 HU"},
    {mysqlName: "SCAN20", intValue: 20, displayName: "Pack 03 Track"},
    {mysqlName: "SCAN21", intValue: 21, displayName: "Ship Sorter HU"},
    {mysqlName: "SCAN22", intValue: 22, displayName: "Ship Sorter Track"}
];

var websiteTitle = "KOZ Service / Monitor";

const wsEndpoints = {
    base: "wss://koz-software.com",
    paths: {
        monitorRemoteListen: "/monitorremotelisten",
    },
};


const websocketMessage = ref({
    msgType: "",
    msgFromService: "",
    msgToService: "",
    msgToUser: "",
    msgFromUser: "",
    CommandObj: {
      cmdName: "",
      cmdMessage: ""
    },
    AlertAndAlarmObj: {
      msgType: "",
      alarmCode: 0,
      alertDevice: "",
      alertMsg: "",
      alertClear: false,
      alertGroup: "",
      jsonMessage: ""
    },
    EstablishConnObj: {
      messageGroup: "",
      userIdName: "",
      userSecurity: "",
      message: ""
    }
});

// var websocketMessage = {
//     msgType: "",
//     msgFromService: "",
//     msgToService: "",
//     CommandObj:{
//         cmdName: "",
//         cmdMessage: ""
//     },
//     AlertAndAlarmObj:{
//         msgType: "",
//         alarmCode: 0,
//         alertDevice: "",
//         alertMsg: "",
//         alertClear: false
//     },
//     EstablishConnObj:{
//         socketArea: "",
//         message: ""
//     }
// };

var isScreenSmall = false;

//leave these function alone they are used to return the data above to vue pages
export default{
    async validateAuthLevel(security_group){
        await auth.getUser().then(user=>{
            console.log(user);
            if(security_group == "any")
            {
                return true;
            }
            else if(securityTypes[security_group].includes(user.data.sim_user_security))
            {
                return true;
            }
            else
            {
                return false;
            }
        })
    },

    getActiveStatGroupsData(){
        return arrClone(StatGroupData);
    },
    getActiveScannerData(){
        return arrClone(ActiveScannersData);
    },
    GeneratePasswordHash(password){
        let hash = CryptoJS.HmacSHA256(password, KEY);
        let base64Hash = CryptoJS.enc.Base64.stringify(hash);
        // console.log(base64Hash);
        return base64Hash;
    },
    setScreenSize(isScreenViewSmall) {
        isScreenSmall = isScreenViewSmall;
    },
    getScreenSizeSmall(){
        return isScreenSmall;
    },
    // getPaths(){
    //     return tmpJson;
    // },
    getAmtOfScanners(){
        return AmtOfScanners;
    },
    getProdCountsInfo(){
        return prodJson;
    },
    getTitle(){
        return websiteTitle;
    },
    getScannerNames(){
        return scannerNames;
    },
    getInternalDivertMap(){
        return internalDivertMap;
    },
    getInternalDivertMapReferenceObject(){
        return internalDivertMapReferenceObject;
    },
    getScannerZones(){
        return scannerZones;
    },
    getWebsiteVersion(){
        return websiteVersion;
    },
    checkWebsiteVersion(){
        axios.get("api/Admin/WebsiteVersion").then(resp=>{
            if(resp.data.data != websiteVersion){
                alert("You are Viewing An Old Version Of KOZ-WEB Press OK To Load New Version.");
                location.reload();
            }
        })
    },

    getWsUrl(endpointKey) {
        const path = wsEndpoints.paths[endpointKey];
      
        if (!path) {
          throw new Error(`WebSocket endpoint "${endpointKey}" not found.`);
        }
      
        return `${wsEndpoints.base}${path}`;
    },

    getPickMonitorMap(){
        return pickingMonitorMap;
    },
    getShippingMonitorMap(){
        return shippingMonitorMap;
    },
    getOSData(){
        return osMonitorNames;
    },
    getOSMonitorMap(){
        return osMonitorMap;
    },
    getPackData(){
        return packMonitorNames;
    },
    getPackMonitorMap(){
        return packMonitorMap;
    },
    getWebsocketMessageBase(){
        return websocketMessage;
    },
    getNumberOfDays() {
        return [{ disp: `Active Data < ${numberOfDays} Days`, Reporting: "0" },
        { disp: `Archived Data > ${numberOfDays} Days`, Reporting: "1" }];
    },
    getNumberOfDaysAndCurrent() {
        return [{ disp: "Current Map", radioBtnValue: "Current Map" },
            { disp: `Active Data < ${numberOfDays} Days`, radioBtnValue: "0" },
        { disp: `Archived Data > ${numberOfDays} Days`, radioBtnValue: "1" }];
    },
    async isSomeoneLoggedIn() {
        return await auth.getUser();
    },
}