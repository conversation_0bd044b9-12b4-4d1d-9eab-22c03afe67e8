<template>
  <div>
    <DxLoadPanel
      :height="500"
      :width="700"
      :visible="loadingVisible"
      :show-indicator="true"
      :show-pane="true"
      :shading="true"
      shading-color="rgba(0,0,0,0.4)"
    />
    <!-- <h2 class="content-block">{{userInfo.sim_first_name}}'s Profile</h2> -->
    <div class="content-block dx-card responsive-paddings">
      <p>
        <b>If any of the below items need to be changed contact your system administrator.</b><br>
        <b>Name:</b> {{userInfo.sim_first_name}} {{userInfo.sim_last_name}}<br>
        <b>Email:</b> {{userInfo.sim_email}}<br>
        <br /><br /><b>Work In Progress</b>
      </p>
    </div>


  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import auth from '../auth';
import notify from 'devextreme/ui/notify';
import databaseName from '../myFunctions/databaseName';
import { DxLoadPanel } from 'devextreme-vue/load-panel';

const router = useRouter();
const userInfo = ref(Object);
const loadingVisible = ref(false);


    
// this.databaseInfo = databaseName.getPaths();
databaseName.checkWebsiteVersion();

databaseName.isSomeoneLoggedIn().then(user => {
  console.log(user);
  if (user.data.sim_first_name == null) {
    let sessionStorageUser = sessionStorage.getItem("KOZ_SERVICE_AUTH");
    let sessionStorageJSON = JSON.parse(sessionStorageUser);

    if(sessionStorageJSON != null)
    {
      auth._simUser = sessionStorageJSON.data;
      if(databaseName.validateAuthLevel('create_modify'))
      {
        //put primer stuff in here for like functions to call to get data
        //'any' value passed to function will allow auth for anyuser
        getUser();
      }
      else
      {
        notify('Your Account Does NOT Have Acces To This Page', 'warning', 10000);
        router.push('/home');
      }
    }
    else
    {
      notify('Please Log In To Use The Service Site', 'warning', 10000);
      router.push('/login-form');
    }
  }
  else
  {
    if(databaseName.validateAuthLevel('create_modify'))
    {
      getUser();
    }
    else
    {
      notify('Your Account Does NOT Have Acces To This Page', 'warning', 10000);
      router.push('/home');
    }
  }

});

//this function gets the user's info to display on the page
const getUser = async() => {
  let temp = await auth.getUser();
  userInfo.value = temp.data;

  if(userInfo.value.userIdName === 'view only'){
    alert("You are required to login to view your profile.");
    router.push('/login-form');
  }
};


</script>

<style lang="scss">
#changePassForm{
  width: 50%;
  position: relative;
  margin: 20px auto;
  padding: 20px 10px 10px 10px;
  border: 2px solid #3aafa9; 
  border-radius: 25px;
  height: auto;
}
</style>