// Test script to demonstrate the unified authentication system
import auth2 from './src/auth2.js';

console.log('=== Unified Authentication System Test ===\n');

// Example of how to use the new unified auth system
async function testUnifiedAuth() {
  console.log('1. Testing Internal User Login:');
  
  // Mock internal user login (this would normally make an API call)
  const internalResult = await auth2.logIn('<EMAIL>', 'password123', 'internal');
  
  if (internalResult.isOk) {
    console.log('✅ Internal login successful');
    console.log('📋 User data with generic fields:');
    console.log(`   - first_name: ${internalResult.data.first_name}`);
    console.log(`   - last_name: ${internalResult.data.last_name}`);
    console.log(`   - email: ${internalResult.data.email}`);
    console.log(`   - company_name: ${internalResult.data.company_name}`);
    console.log(`   - security_level: ${internalResult.data.security_level}`);
    console.log(`   - user_type: ${internalResult.data.user_type}`);
    
    console.log('\n📋 Backward compatibility - original fields still available:');
    console.log(`   - sim_first_name: ${internalResult.data.sim_first_name}`);
    console.log(`   - sim_email: ${internalResult.data.sim_email}`);
    console.log(`   - sim_user_security: ${internalResult.data.sim_user_security}`);
  }
  
  console.log('\n2. Testing Customer User Login:');
  
  // Mock customer user login
  const customerResult = await auth2.logIn('<EMAIL>', 'password456', 'customer');
  
  if (customerResult.isOk) {
    console.log('✅ Customer login successful');
    console.log('📋 User data with generic fields:');
    console.log(`   - first_name: ${customerResult.data.first_name}`);
    console.log(`   - last_name: ${customerResult.data.last_name}`);
    console.log(`   - email: ${customerResult.data.email}`);
    console.log(`   - company_name: ${customerResult.data.company_name}`);
    console.log(`   - security_level: ${customerResult.data.security_level}`);
    console.log(`   - user_type: ${customerResult.data.user_type}`);
    
    console.log('\n📋 Backward compatibility - original fields still available:');
    console.log(`   - customers_first_name: ${customerResult.data.customers_first_name}`);
    console.log(`   - customers_email: ${customerResult.data.customers_email}`);
    console.log(`   - customer_name: ${customerResult.data.customer_name}`);
  }
  
  console.log('\n3. Testing Unified User Access:');
  
  // Now you can access user data using generic field names regardless of user type
  const currentUser = await auth2.getUser();
  if (currentUser.isOk) {
    console.log('✅ Current user retrieved successfully');
    console.log(`👤 Welcome ${currentUser.data.first_name} ${currentUser.data.last_name}!`);
    console.log(`📧 Email: ${currentUser.data.email}`);
    console.log(`🏢 Company: ${currentUser.data.company_name}`);
    console.log(`🔒 Security Level: ${currentUser.data.security_level}`);
    console.log(`👥 User Type: ${currentUser.data.user_type}`);
  }
}

// Benefits of the unified system:
console.log('\n=== Benefits of Unified Authentication ===');
console.log('✅ Single UnifiedAuth class instead of separate CustomerAuth and InternalAuth');
console.log('✅ Generic field names (first_name, last_name, email, etc.) work for both user types');
console.log('✅ Backward compatibility maintained - original field names still available');
console.log('✅ Simplified AuthManager - no need to manage separate auth instances');
console.log('✅ Consistent user object structure regardless of user type');
console.log('✅ Easy to extend for additional user types in the future');

// Example of how to use generic fields in your components:
console.log('\n=== Usage Example in Components ===');
console.log(`
// Before (had to check user type):
if (auth2.isInternal()) {
  displayName = user.sim_first_name + ' ' + user.sim_last_name;
} else if (auth2.isCustomer()) {
  displayName = user.customers_first_name + ' ' + user.customers_last_name;
}

// After (works for both user types):
displayName = user.first_name + ' ' + user.last_name;
`);

export { testUnifiedAuth };
