<template>
	<DxLoadPanel :visible="loading" :container="'.content-block'" :position="{ of: '.content-block' }"/>


  <div class="content-block responsive dx-card ">
		<!-- <div class="row" style="padding: 10px;">
			<div>
				<span>Date</span>
				<DxDateBox v-model="date" type="date" styling-mode="underlined" display-format="yyyy-MM-dd" @value-changed="formatDate"/>
			</div>
			<div>
				<DxButton
					:width="100"
					text="Refresh"
					type="default"
					styling-mode="contained"
					@click="fetchData"
				/>
			</div>
		</div> -->
		<DxResponsiveBox class="responsive-box" :screen-by-width="screen" single-column-screen="sm">
			<DxRow :ratio="1"/>
      <DxRow :ratio="1"/>
      <DxRow :ratio="1"/>
			<DxRow :ratio="1"/>

      <DxCol :ratio="1"/>
      <DxCol :ratio="1"/>
      <DxCol :ratio="1"/>
      <DxCol :ratio="1"/>
			<DxCol :ratio="1"/>
			<DxCol :ratio="1"/>
			<DxCol :ratio="1"/>
			<DxCol :ratio="1"/>

			<!-- Summary Blocks -->
			<DxItem :location="{ row: 0, col: 0 ,colspan: 2 }">
				<template #default>
					<div class="card-wrap">
						<div class="card">
							<div class="card-title">Open Orders</div>
							<p class="card-value neon">{{ formatNumber(openTotal) }}</p>
							<!-- <p class="card-desc">Awaiting processing</p> -->
						</div>
					</div>
				</template>
			</DxItem>

			<DxItem :location="{ row: 0, col: 2 ,colspan: 2  }">
				<template #default>
					<div class="card-wrap">
						<div class="card">
							<div class="card-title">In Progress</div>
							<p class="card-value green">{{ formatNumber(inProgressTotal) }}</p>
							<!-- <p class="card-desc">Currently being processed</p> -->
						</div>
					</div>
				</template>
			</DxItem>

			<DxItem :location="{ row: 0, col: 4 ,colspan: 2 }">
				<template #default>
					<div class="card-wrap">
						<div class="card">
							<div class="card-title">Completed Orders</div>
							<p class="card-value orange">{{ formatNumber(completeTotal) }}</p>
							<!-- <p class="card-desc">Completed orders</p> -->
						</div>
					</div>
				</template>
			</DxItem>

			<DxItem :location="{ row: 0, col: 6 ,colspan: 2 }" >
				<template #default>
					<div class="card-wrap">
						<div class="card">
							<div class="card-title">Total Orders</div>
							<p class="card-value blue">{{ formatNumber(openTotal + inProgressTotal + completeTotal) }}</p>
							<!-- <p class="card-desc">Total in system</p> -->
						</div>
					</div>
				</template>
			</DxItem>

			<!-- Order Start Stations -->
			<DxItem
				:location="{ row: 1, col: 0 ,colspan: 2 }">
				<template #default>
					<div class="card-wrap">
						<div class="card">
							<div class="card-title">Order Start Stations</div>
							<DxDataGrid :data-source="orderStartStations" :show-borders="false" :column-auto-width="true" :width="'100%'">
								<DxColumn data-field="order_start_station_name" caption="Station" />
								<DxColumn data-field="station_count" caption="Count" />
								<DxExport :enabled="false" />
								<DxPaging :enabled="false" />
								<DxScrolling mode="virtual"/>
							</DxDataGrid>
						</div>
					</div>
				</template>
			</DxItem>

			<!-- Pack Stations -->
			<DxItem
				:location="{ row: 1, col: 2, colspan: 2 }">
				<template #default>
					<div class="card-wrap">
						<div class="card">
							<div class="card-title">Pack Stations</div>
							<DxDataGrid style="height: 165px;" :data-source="packStations" :show-borders="false" :column-auto-width="true" >
								<DxColumn data-field="pack_station_name" caption="Station" />
								<DxColumn data-field="station_count" caption="Count" />
								<DxExport :enabled="false" />
								<DxPaging :enabled="true" />
							</DxDataGrid>
						</div>
					</div>
				</template>
			</DxItem>
		
			<!-- Charts Section -->
			<DxItem :location="{ row: 1, col: 4 ,colspan: 4 }">
				<template #default>
					<div class="card-wrap">
						<div class="card">
							<DxPieChart class="card-desc" style="height: 200px;" v-if="chartReady && statusData && statusData.length"  
								:data-source="statusData" title="Order Status Overview" :customize-point="customizeStatusPoint"
								:redraw-on-resize="true" resolve-label-overlapping="shift" >
								<DxSeries argument-field="name" value-field="value" type="doughnut">
									<DxLabel :visible="true" format="fixedPoint" :customizeText="customizeLabel">
										<DxConnector :visible="true" :width="1" />
									</DxLabel>
								</DxSeries>
								<DxTitle text="Order Status Overview" vertical-alignment="top" horizontal-alignment="left" margin="8"></DxTitle>
								<DxLegend :column-count="4" orientation="horizontal" item-text-position="right" horizontal-alignment="center" vertical-alignment="bottom" /> 
								<DxTooltip :enabled="true" :customize-tooltip="customizeTooltip"  cornerRadius="10" color="#181818"/>
							</DxPieChart>
						</div>
					</div>
				</template>
			</DxItem>
			
			<DxItem :location="{ row: 2, col: 0, colspan: 4 }" >
				<template #default>
					<div class="card-wrap">
						<div class="card">
							<DxChart v-if="chartReady && carrierComparisonData && carrierComparisonData.length" :data-source="carrierComparisonData" style="height: 200px;" :alignment="'center'">
								<!-- <DxValueAxis :title="'Orders'" /> -->
								<DxSeries argument-field="name" value-field="Open" name="Open" type="bar" color="#97C95C" />
								<DxSeries argument-field="name" value-field="InProgress" name="In Progress" type="bar"  color="#3aafa9" />
								<DxSeries argument-field="name"  value-field="Complete"  name="Complete"  type="bar"  color="#fd7e14"  />
								<DxTitle text="Order Carrier Distribution" vertical-alignment="top" horizontal-alignment="left" margin="8"></DxTitle>
								<DxLegend :column-count="3" orientation="horizontal" item-text-position="right" horizontal-alignment="center" vertical-alignment="bottom" /> 
								<DxTooltip :enabled="true" :customize-tooltip="customizeCarrierTooltip" cornerRadius="10" color="#181818"/>
							</DxChart>
						</div>
					</div>
				</template>
			</DxItem>
			
			
		
			<DxItem :location="{ row: 2, col: 4, colspan:4}" >
				<template #default>
					<div class="card-wrap">
						<div class="card">
							<DxChart v-if="chartReady && combinedContainerData && combinedContainerData.length" :data-source="combinedContainerData" style="height: 200px;">
								<DxCommonSeriesSettings type="bar" :bar-width="70" :hoverMode="'allArgumentPoints'"/>
								<DxSeries argument-field="containerType" value-field="Open" name="Open" color="#97C95C" />
								<DxSeries argument-field="containerType" value-field="InProgress" name="In Progress" color="#3aafa9" />
								<DxSeries argument-field="containerType" value-field="Complete" name="Complete" color="#fd7e14" />
								<DxTitle text="Orders By Container & Status" vertical-alignment="top" horizontal-alignment="left" margin="8"></DxTitle>
								<DxLegend :visible="true" orientation="horizontal" item-text-position="right" horizontal-alignment="center" vertical-alignment="bottom"/>
								<DxTooltip :enabled="true" :shared="true" :customize-tooltip="customizeCombinedTooltip"  cornerRadius="10" color="#181818"/>
							</DxChart>
						</div>
					</div>
				</template>
			</DxItem>

			<DxItem :location="{ row: 3, col: 0, colspan: 8}" >
				<template #default>
					<div class="card-wrap">
						<div class="card">
							<DxSankey v-if="chartReady && links && links.length" id="sankey" :data-source="links" :source-field="'source'" :target-field="'target'"
									:weight-field="'volume'" :node="nodeOptions" :link="linkOptions" adaptive-layout="true" >

								<DxTitle text="Order Flow" vertical-alignment="top" horizontal-alignment="left" margin="8"></DxTitle>
								<DxTooltip :enabled="true" :customize-link-tooltip="customizeSankeyTooltip"  cornerRadius="10" color="#181818"/>
							</DxSankey>
						</div>
					</div>
				</template>
			</DxItem>


			
      
    </DxResponsiveBox>
    <!-- <DxPieChart :data-source="statusData"  title="Area of Countries" >
			<DxSeries argument-field="name" value-field="value" type="doughnut">
				<DxLabel :visible="true" format="fixedPoint">
					<DxConnector :visible="true" :width="1" />
				</DxLabel>
			</DxSeries>
			<DxLegend :column-count="4" orientation="horizontal" item-text-position="right" horizontal-alignment="center" vertical-alignment="bottom" /> 
			<DxTooltip :enabled="true" :customize-tooltip="customizeTooltip" />
		</DxPieChart>
     -->
		
    
  </div>
</template>

<script setup>
	/*=====================================================================
    IMPORTS
 	=====================================================================*/
	// Vue core
  import { ref, computed, onMounted, watch, onUnmounted } from 'vue';

	// UI components & utilities
	import notify from 'devextreme/ui/notify';
	import { DxPieChart,DxSeries,DxLabel,DxLegend,DxExport,DxTooltip,DxFont, DxTitle, DxConnector } from 'devextreme-vue/pie-chart';
	import { DxChart,DxArgumentAxis,DxValueAxis, DxCommonSeriesSettings } from 'devextreme-vue/chart';
	import { DxSankey } from 'devextreme-vue/sankey';
	import { DxDataGrid, DxColumn,DxPaging, DxScrolling} from 'devextreme-vue/data-grid';
	import { DxResponsiveBox,  DxItem, DxLocation, DxCol, DxRow} from 'devextreme-vue/responsive-box';
	import { DxLoadPanel } from 'devextreme-vue/load-panel';
	import { DxDateBox }from 'devextreme-vue/date-box';
	import { DxButton } from 'devextreme-vue/button';

		// Composables
	import { useScreenSize } from '@/composables/useScreenSize';
	import { useRestAPI } from '@/composables/useRestAPI';
	import auth from '../auth'
	import { useRouter } from 'vue-router';
	/*=====================================================================
    COMPOSABLE DEFINITIONS (EXPOSED STATES AND FUNCTIONS)  
  =====================================================================*/
	const { isMobile } = useScreenSize()
	const api = useRestAPI();
	const router = useRouter();
	
	/*=====================================================================
    LIFECYCLE HOOKS 
  =====================================================================*/
	// Lifecycle hooks
	onMounted(async () => {
		const user = await auth.getUser()
		if (!user.data.auth_token) { router.push('/login-form'); return }
		
		// This uses the DevExtreme global object to define a palette
		if (window.DevExpress && window.DevExpress.viz && window.DevExpress.viz.registerPalette) {
			window.DevExpress.viz.registerPalette('Custom', {
				simpleSet: ['#97C95C', '#3aafa9', '#fd7e14']
			});
		}
		date.value = getFormattedDate(date.value);

		await fetchData();
		
		// Update the time every minute
		timeUpdateInterval = setInterval(() => {
			currentDateTime.value = new Date().toLocaleString();
		}, 60000);
	});

	onUnmounted(() => {
		if (timeUpdateInterval) {
			clearInterval(timeUpdateInterval);
		}
	});

	/*=====================================================================
    ELEMENT REFS
  =====================================================================*/


	/*=====================================================================
    REFS FOR LOCAL STATE
  =====================================================================*/
	const date = ref(new Date()); // Initialize in YYYY-MM-DD format
	const chartReady = ref(false);
	const loading = ref(true);
	const dashboardData = ref(null);
	const currentDateTime = ref(new Date().toLocaleString());
	let timeUpdateInterval = null;
	const nodeOptions = { width: 10, padding: 30, title: { font: { size: 14 } } };
	const linkOptions = { colorMode: 'gradient' };
	const notifyOptions = ref(
		{ 
			position: { my: "bottom", at: "bottom", of: ".content-block", offset: { x: 0, y: 10 }}, width: 'auto',
			animation: { show: { type: "fade", duration: 800, from: 0, to: 1 }, hide: { type: "fade", duration: 800, from: 1, to: 0 } }
		}
	)


	/*=====================================================================
		FUNCTIONS
	=====================================================================*/

	const showNotification = (result, successMessage) => {
		if (result.success) {
			notify({ ...notifyOptions.value, message: successMessage, type: 'success', displayTime: 2000 });
		} else {
			notify({ ...notifyOptions.value, message: result.error, type: 'error', displayTime: 3000 });
		}
	};

	const getFormattedDate = (date) => {
		if (!(date instanceof Date) || isNaN(date)) return null; // Ensure it's a valid Date object
		const year = date.getFullYear();
		const month = String(date.getMonth() + 1).padStart(2, '0'); // Ensure MM format
		const day = String(date.getDate()).padStart(2, '0'); // Ensure DD format
		return `${year}-${month}-${day}`;
	};

	function formatDate(e) {
		if (e.value) {
			const date = new Date(e.value); 
			date.value = getFormattedDate(date);
		} else {
			date.value = null; 
		}
	}
	// Fetch dashboard data
	const fetchData = async () => {
		loading.value = true;
		chartReady.value = false; // Reset chart ready state to prevent updates on invalid charts
  
		try {
			// In a real app, this would be an API call
			// For this example, we're using static data
			
			dashboardData.value = {
  "OrderStartCounts": [
    { "order_start_station_name": "OrderStart01", "station_count": 1200 },
    { "order_start_station_name": "OrderStart02", "station_count": 800 }
  ],
  "PackStationCounts": [
    { "pack_station_name": "PackStation01", "station_count": 300 },
    { "pack_station_name": "PackStation02", "station_count": 250 },
    { "pack_station_name": "PackStation03", "station_count": 50 },
    { "pack_station_name": "PackStation04", "station_count": 1800 },
    { "pack_station_name": "PackStation05", "station_count": 3580 },
    { "pack_station_name": "PackStation06", "station_count": 458 }
  ],
  "OpenCounts": {
    "total_count": 3200,
    "shipping_carriers": [
      { "shipping_carrier": "DHL", "count": 865 },
      { "shipping_carrier": "FEDEX", "count": 779 },
      { "shipping_carrier": "UPS", "count": 908 },
      { "shipping_carrier": "USPS", "count": 648 }
    ],
    "container_types": [
      { "container_type": "8x8x8", "count": 995 },
      { "container_type": "TOTE", "count": 1081 },
      { "container_type": "18x8x8", "count": 649 },
      { "container_type": "18x8x6", "count": 475 }
    ],
    "combined_stats": [
      { "shipping_carrier": "DHL", "container_type": "8x8x8", "count": 346 },
      { "shipping_carrier": "DHL", "container_type": "TOTE", "count": 216 },
      { "shipping_carrier": "DHL", "container_type": "18x8x8", "count": 303 },
      { "shipping_carrier": "FEDEX", "container_type": "8x8x8", "count": 390 },
      { "shipping_carrier": "FEDEX", "container_type": "TOTE", "count": 173 },
      { "shipping_carrier": "FEDEX", "container_type": "18x8x6", "count": 216 },
      { "shipping_carrier": "UPS", "container_type": "8x8x8", "count": 259 },
      { "shipping_carrier": "UPS", "container_type": "TOTE", "count": 303 },
      { "shipping_carrier": "UPS", "container_type": "18x8x8", "count": 346 },
      { "shipping_carrier": "USPS", "container_type": "TOTE", "count": 389 },
      { "shipping_carrier": "USPS", "container_type": "18x8x6", "count": 259 }
    ]
  },
  "InProgressCounts": {
    "total_count": 669,
    "shipping_carriers": [
      { "shipping_carrier": "DHL", "count": 530 },
      { "shipping_carrier": "FEDEX", "count": 60 },
      { "shipping_carrier": "UPS", "count": 40 },
      { "shipping_carrier": "USPS", "count": 39 }
    ],
    "container_types": [
      { "container_type": "8x8x8", "count": 549 },
      { "container_type": "TOTE", "count": 80 },
      { "container_type": "18x8x6", "count": 40 }
    ],
    "combined_stats": [
      { "shipping_carrier": "DHL", "container_type": "8x8x8", "count": 500 },
      { "shipping_carrier": "DHL", "container_type": "TOTE", "count": 30 },
      { "shipping_carrier": "FEDEX", "container_type": "8x8x8", "count": 40 },
      { "shipping_carrier": "FEDEX", "container_type": "TOTE", "count": 20 },
      { "shipping_carrier": "UPS", "container_type": "18x8x6", "count": 40 },
      { "shipping_carrier": "USPS", "container_type": "TOTE", "count": 30 },
      { "shipping_carrier": "USPS", "container_type": "8x8x8", "count": 9 }
    ]
  },
  "CompleteCounts": {
    "total_count": 6345,
    "shipping_carriers": [
      { "shipping_carrier": "DHL", "count": 2000 },
      { "shipping_carrier": "FEDEX", "count": 1800 },
      { "shipping_carrier": "UPS", "count": 1500 },
      { "shipping_carrier": "USPS", "count": 1045 }
    ],
    "container_types": [
      { "container_type": "8x8x8", "count": 2500 },
      { "container_type": "TOTE", "count": 2500 },
      { "container_type": "18x8x8", "count": 1345 }
    ],
    "combined_stats": [
      { "shipping_carrier": "DHL", "container_type": "8x8x8", "count": 1000 },
      { "shipping_carrier": "DHL", "container_type": "TOTE", "count": 500 },
      { "shipping_carrier": "DHL", "container_type": "18x8x8", "count": 500 },
      { "shipping_carrier": "FEDEX", "container_type": "8x8x8", "count": 900 },
      { "shipping_carrier": "FEDEX", "container_type": "TOTE", "count": 500 },
      { "shipping_carrier": "FEDEX", "container_type": "18x8x8", "count": 400 },
      { "shipping_carrier": "UPS", "container_type": "8x8x8", "count": 600 },
      { "shipping_carrier": "UPS", "container_type": "TOTE", "count": 700 },
      { "shipping_carrier": "UPS", "container_type": "18x8x8", "count": 200 },
      { "shipping_carrier": "USPS", "container_type": "TOTE", "count": 800 },
      { "shipping_carrier": "USPS", "container_type": "18x8x8", "count": 245 }
    ]
  }
};



				loading.value = false;
				chartReady.value = true;
		} catch (error) {
			console.error('Error fetching data:', error);
			loading.value = false;
		}
	};

	const processCarrierData = (carrierData) => {
		const result = [];
		const carriers = {};
		
		carrierData.forEach(item => {
			const carrier = item.shipping_carrier.toUpperCase();
			if (carriers[carrier]) {
				carriers[carrier] += item.count;
			} else {
				carriers[carrier] = item.count;
			}
		});
		
		Object.keys(carriers).forEach(carrier => {
			result.push({
				name: carrier,
				value: carriers[carrier]
			});
		});
		
		return result;
	};

	// Utilities
	const formatNumber = (value) => (value.toLocaleString());
	const customizeTooltip = (arg) => ({text: `${arg.argumentText}: ${arg.valueText.toLocaleString()}`});
	const customizeLabel = (arg) => (`${arg.valueText.toLocaleString()} (${arg.percentText})`);
	const screen = (width) => (isMobile.value ? 'sm' : 'lg');
	// Custom sankey graph tooltip
	const customizeSankeyTooltip = ({ source, target, weight }) => (
		{ html: `<b>From:</b> ${source}<br/><b>To:</b> ${target}<br/><b>Volume:</b> ${weight} orders` }
	);
	const customizeCarrierTooltip = (arg) => {
		if (arg.seriesName) {
			let total = 0;
			let tooltipText = `<b>${arg.argumentText}</b><br><span style="color: ${arg.point.getColor()}">\u25CF</span> ${arg.seriesName}: <b>${arg.valueText.toLocaleString()}</b><br>`;
			
			if (arg.seriesName === 'Open') {
				total += arg.value;
				if (carrierComparisonData.value.length > 0) {
					const carrier = carrierComparisonData.value.find(c => c.name === arg.argumentText);
					if (carrier) {
						total += carrier.InProgress || 0;
						total += carrier.Complete || 0;
					}
				}
			} else if (arg.seriesName === 'In Progress') {
				total += arg.value;
				if (carrierComparisonData.value.length > 0) {
					const carrier = carrierComparisonData.value.find(c => c.name === arg.argumentText);
					if (carrier) {
						total += carrier.Open || 0;
						total += carrier.Complete || 0;
					}
				}
			} else if (arg.seriesName === 'Complete') {
				total += arg.value;
				if (carrierComparisonData.value.length > 0) {
					const carrier = carrierComparisonData.value.find(c => c.name === arg.argumentText);
					if (carrier) {
						total += carrier.Open || 0;
						total += carrier.InProgress || 0;
					}
				}
			}
			
			tooltipText += `Total for ${arg.argumentText}: <b>${total.toLocaleString()}</b>`;
			return { html: tooltipText };
		}
		
		return { text: `${arg.argumentText}: ${arg.valueText.toLocaleString()}` };
	};

	const customizeCombinedTooltip = (pointInfo) => {
		const items = pointInfo.points.map(point => {
			let color;
			if (point.seriesName === 'Open') color = '#97C95C';
			else if (point.seriesName === 'In Progress') color = '#3aafa9';
			else if (point.seriesName === 'Complete') color = '#fd7e14';
			
			return `<tr>
				<td><span style="color: ${color}">●</span> ${point.seriesName}:</td>
				<td style="text-align: right; padding-left: 10px;"><b>${point.value.toLocaleString()}</b></td>
			</tr>`;
		});
		
		// Calculate total for this container type
		const total = pointInfo.points.reduce((sum, point) => sum + point.value, 0);
		
		return {
			html: `
				<div>
					<div style="font-weight: bold; margin-bottom: 5px;">${pointInfo.argumentText}</div>
					<table>
						${items.join('')}
						<tr>
							<td style="border-top: 1px solid #d3d3d3; padding-top: 3px;">Total:</td>
							<td style="border-top: 1px solid #d3d3d3; padding-top: 3px; text-align: right; padding-left: 10px;">
								<b>${total.toLocaleString()}</b>
							</td>
						</tr>
					</table>
				</div>
			`
		};
	};

	const customizeStatusPoint = (pointInfo) => {
		const colorMap = {
			'Open': '#97C95C',
			'In Progress': '#3aafa9', 
			'Complete': '#fd7e14'
		};
		
		if (colorMap[pointInfo.argument]) {
			return { color: colorMap[pointInfo.argument] };
		}
		
		return {};
	};

	
	/*=====================================================================
    COMPUTED PROPERTIES 
  =====================================================================*/
	const openTotal = computed(() => 
		dashboardData.value?.OpenCounts?.total_count || 0
	);

	const completeTotal = computed(() => 
		dashboardData.value?.CompleteCounts?.total_count || 0
	);

	const completeCarrierData = computed(() => {
		if (!dashboardData.value?.CompleteCounts?.shipping_carriers) return [];
		return processCarrierData(dashboardData.value.CompleteCounts.shipping_carriers);
	});

	const completeContainerData = computed(() => {
		if (!dashboardData.value?.CompleteCounts?.container_types) return [];
		return dashboardData.value.CompleteCounts.container_types.map(item => ({
			name: item.container_type,
			value: item.count
		}));
	});

	const hasCompleteData = computed(() => 
		!!dashboardData.value?.CompleteCounts?.container_types
	);
	const inProgressTotal = computed(() => 
		dashboardData.value?.InProgressCounts?.total_count || 0
	);

	const statusData = computed(() => {
		if (!dashboardData.value) return [];
		const data = [
			{ name: 'Open', value: openTotal.value},
			{ name: 'In Progress', value: inProgressTotal.value},
			{ name: 'Complete', value: completeTotal.value}
		];
		
		return data;
	});

	const openCarrierData = computed(() => {
		if (!dashboardData.value?.OpenCounts?.shipping_carriers) return [];
		return processCarrierData(dashboardData.value.OpenCounts.shipping_carriers);
	});

	const inProgressCarrierData = computed(() => {
		if (!dashboardData.value?.InProgressCounts?.shipping_carriers) return [];
		return processCarrierData(dashboardData.value.InProgressCounts.shipping_carriers);
	});

	const openContainerData = computed(() => {
		if (!dashboardData.value?.OpenCounts?.container_types) return [];
		return dashboardData.value.OpenCounts.container_types.map(item => ({
			name: item.container_type,
			value: item.count
		}));
	});

	const inProgressContainerData = computed(() => {
		if (!dashboardData.value?.InProgressCounts?.container_types) return [];
		return dashboardData.value.InProgressCounts.container_types.map(item => ({
			name: item.container_type,
			value: item.count
		}));
	});

	const carrierComparisonData = computed(() => {
		if (!openCarrierData.value) return [];
		
		const allCarriers = new Set([
			...openCarrierData.value.map(item => item.name),
			...inProgressCarrierData.value.map(item => item.name),
			...(completeCarrierData.value ? completeCarrierData.value.map(item => item.name) : [])
		]);
		
		const result = [];
		allCarriers.forEach(carrier => {
			const openItem = openCarrierData.value.find(item => item.name === carrier);
			const inProgressItem = inProgressCarrierData.value.find(item => item.name === carrier);
			const completeItem = completeCarrierData.value ? 
				completeCarrierData.value.find(item => item.name === carrier) : null;
			
			result.push({
				name: carrier,
				Open: openItem ? openItem.value : 0,
				InProgress: inProgressItem ? inProgressItem.value : 0,
				Complete: completeItem ? completeItem.value : 0
			});
		});
		
		return result;
	});

	const combinedContainerData = computed(() => {
		if (!dashboardData.value) {
			return [];
		}
		
		// Extract container types from all statuses
		const containerTypes = new Set();
		
		if (dashboardData.value.OpenCounts?.container_types) {
			dashboardData.value.OpenCounts.container_types.forEach(item => {
				containerTypes.add(item.container_type);
			});
		}
		
		if (dashboardData.value.InProgressCounts?.container_types) {
			dashboardData.value.InProgressCounts.container_types.forEach(item => {
				containerTypes.add(item.container_type);
			});
		}
		
		if (dashboardData.value.CompleteCounts?.container_types) {
			dashboardData.value.CompleteCounts.container_types.forEach(item => {
				containerTypes.add(item.container_type);
			});
		}
		
		// Create combined data for each container type
		const result = [];
		containerTypes.forEach(containerType => {
			const openItem = dashboardData.value.OpenCounts?.container_types?.find(
				item => item.container_type === containerType
			);
			
			const inProgressItem = dashboardData.value.InProgressCounts?.container_types?.find(
				item => item.container_type === containerType
			);
			
			const completeItem = dashboardData.value.CompleteCounts?.container_types?.find(
				item => item.container_type === containerType
			);
			
			// Only add if container type has data in at least one status
			if (openItem?.count > 0 || inProgressItem?.count > 0 || completeItem?.count > 0) {
				result.push({
					containerType,
					Open: openItem?.count || 0,
					InProgress: inProgressItem?.count || 0,
					Complete: completeItem?.count || 0,
					Total: (openItem?.count || 0) + (inProgressItem?.count || 0) + (completeItem?.count || 0)
				});
			}
		});

		// Sort by total count descending
		return result.sort((a, b) => b.Total - a.Total);
	});

	// Prepare sankey data
	const links = computed(() => {

		const result = [];
		
		// Add status to container type links for Open
		if (dashboardData.value.OpenCounts?.container_types) {
			dashboardData.value.OpenCounts.container_types.forEach(container => {
				if (container.count > 0) {
					result.push({
						source: 'Open',
						target: container.container_type,
						volume: container.count
					});
				}
			});
		}
		
		// Add status to container type links for In Progress
		if (dashboardData.value.InProgressCounts?.container_types) {
			dashboardData.value.InProgressCounts.container_types.forEach(container => {
				if (container.count > 0) {
					result.push({
						source: 'In Progress',
						target: container.container_type ,
						volume: container.count
					});
				}
			});
		}
		
		// Add status to container type links for Complete
		if (dashboardData.value.CompleteCounts?.container_types) {
			dashboardData.value.CompleteCounts.container_types.forEach(container => {
				if (container.count > 0) {
					result.push({
						source: 'Complete',
						target: container.container_type,
						volume: container.count
					});
				}
			});
		}
		
		// Add container type to carrier links for Open status
		if (dashboardData.value.OpenCounts?.combined_stats) {
			dashboardData.value.OpenCounts.combined_stats.forEach(stat => {
				if (stat.count > 0) {
					result.push({
						source: stat.container_type,
						target: stat.shipping_carrier.toUpperCase(),
						volume: stat.count
					});
				}
			});
		}
		
		// Add container type to carrier links for In Progress status
		if (dashboardData.value.InProgressCounts?.combined_stats) {
			dashboardData.value.InProgressCounts.combined_stats.forEach(stat => {
				if (stat.count > 0) {
					result.push({
						source: stat.container_type ,
						target: stat.shipping_carrier.toUpperCase() ,
						volume: stat.count
					});
				}
			});
		}
		
		// Add container type to carrier links for Complete status
		if (dashboardData.value.CompleteCounts?.combined_stats) {
			dashboardData.value.CompleteCounts.combined_stats.forEach(stat => {
				if (stat.count > 0) {
					result.push({
						source: stat.container_type ,
						target: stat.shipping_carrier.toUpperCase() ,
						volume: stat.count
					});
				}
			});
		}
		
		return result;
	});


	const orderStartStations = computed(() => 
		dashboardData.value?.OrderStartCounts || []
	);

	const packStations = computed(() => 
		dashboardData.value?.PackStationCounts || []
	);



	/*=====================================================================
    WATCHERS 
  =====================================================================*/

	// Add this watch to properly handle chart initialization
	watch(() => loading.value, (newVal) => {
		if (!newVal && statusData.value.length) {
			// Give the DOM time to render before initializing the chart
			setTimeout(() => {
				chartReady.value = true;
			}, 100);
		}
	}, { immediate: true });

</script>

<style scoped lang="scss">
	@import "../themes/generated/variables.additional.scss";

	.content-block {
		display: flex;
		flex-direction: column;
		flex: 1 1 auto;
		min-height: 0;
		overflow: hidden;
		position: relative;
		transition: all 0.3s ease;
		color: rgba($base-text-color, .75)!important;
		padding: 30px;
	}

	.row {
		display: flex;
		gap: 20px;
		flex-shrink: 0; 
		align-items: end;
		color: rgba($base-text-color, 0.75)!important;
	}

	/* Card styles */
	.card {
		background-color: rgba($base-bg-dark, 0.3);
		border: 1px solid $base-border-color;
		color: rgba($base-text-color, .65)!important;
		border-radius: 8px;
		padding: 20px;
		flex: auto;


		&::v-deep(.dxc-title>text, .dxs-title>text) {
			color: rgba($base-text-color, .65)!important;
			fill: rgba($base-text-color, .65)!important;
			font-size: 16px!important;
			font-weight: 400!important;
		}

		&::v-deep(.dxs-title>text) {
			color: rgba($base-text-color, .65)!important;
			fill: rgba($base-text-color, .65)!important;
			font-size: 16px!important;
			font-weight: 400!important;
		}

		&::v-deep(.dxs-labels>g>text) {
			color: rgba($base-text-color, .65)!important;
			fill: rgba($base-text-color, .65)!important;
			font-weight: 400!important;
		}

	}
	.card-wrap{
		padding: 10px;
	display: flex;
	}
	.chart-card {
		// min-height: 400px;
	}

	.card-title {
		font-size: 16px;
		font-weight: 400;
		margin-bottom: 10px;
	}

	.card-value {
		font-size: 24px;
		font-weight: 500;
		margin: 0;
	}

	.card-desc {
		font-size: 14px;

	}

	/* Value colors */
	.blue {
		color: #1FB2F5;
	}

	.green {
		color:$base-accent
	}

	.neon {
		color: #97C95C;
	}
	.orange {
		color: #fd7e14;
	}

	/* DX Controls Override  */
	::v-deep(.dx-texteditor) {
		border: 0px;
		border-radius: 5px;
		background-color: rgba($base-bg-dark, .65);
		padding: 0 10px;

	}
	::v-deep(.dx-texteditor-input) {
		color: rgba($base-text-color, 0.75)!important;
	}		
	.dx-texteditor.dx-editor-filled.dx-state-disabled {
		background-color: rgba($base-bg-dark, .65);
		opacity: 1;
	}
	.dx-texteditor::before {
		content: none;
	}
	.dx-texteditor::after {
		content: none;
	}

	.custom-button.dx-button, .dx-button.dx-button-default , .dx-item-content.dx-button.dx-button-default {
		border-radius: 5px;
		box-shadow: none;
		height: 31px;
		margin-bottom: 1px;
		color: $base-text-color!important; //rgba($base-text-color, alpha($base-text-color)  * 0.85);
	}

	.custom-button {
		border-radius: 5px;
		box-shadow: none;
		height: 31px;
		margin-bottom: 1px;
		color: $base-text-color!important; //rgba($base-text-color, alpha($base-text-color)  * 0.85);
	}

	.custom-button.dx-button-has-icon ,.dx-button-content {
		min-width: unset!important;
		height: unset!important;
	}

	::v-deep(.dx-button-has-icon ){
		width: unset!important;
		height: unset!important;
	}

	::v-deep(.custom-button.dx-button-has-icon .dx-icon) {
		font-size: 10px !important;
		width: unset;
		height: unset;
	}

	::v-deep(.dx-datagrid ){
		color: rgba($base-text-color, 0.75)!important;
		background-color: transparent!important;
	}

	::v-deep(.dx-datagrid > .dx-datagrid-rowsview, .dx-datagrid-rowsview .dx-row) {
		color: rgba($base-text-color, 0.75)!important;
		background-color: transparent!important;
		border: unset!important;
		border-bottom: unset!important;
	}

	::v-deep(.dx-datagrid .dx-row-lines > td){
		border-bottom: unset!important;
	}
	::v-deep(.dx-datagrid .dx-row > td) {
		padding:2px 0;
	}
	::v-deep(.dx-header-row){
		background-color: rgba($base-bg-dark, 0.3);
	}

	::v-deep(.dxc-title) {
	transform:translate(0, 0px) ;
}

</style>