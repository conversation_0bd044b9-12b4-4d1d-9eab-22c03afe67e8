<template>
  <div>
    <div class="content-block">
      <h2>Create Ticket Original</h2> 
    </div>
    <div class="content-block">
      <div v-if="!ticketDetailsArea">  
        <DxButton
          :width="260"
          text="Creat Ticket Manually"
          type="default"
          styling-mode="contained"
          icon="plus"
          @click="modifyTicketDetailsAreaManually(true)"
        />       
      </div>
      <div class="dx-card responsive-paddings">
        <DxLoadPanel
          :height="500"
          :width="700"
          :visible="loadingVisible"
          :show-indicator="true"
          :show-pane="true"
          :shading="true"
          shading-color="rgba(0,0,0,0.4)"
        />
        <div>
          <DxPopup
            v-model:visible="preTicketQuestionsPopup"
            :drag-enabled="false"
            :hide-on-outside-click="false"
            :show-close-button="false"
            :show-title="false"
            height="60%"
            width="60%"
          >
            <h5 style="text-align: center;">Before Creating A Ticket, Please Answer the Following:</h5>
            <div>
              <div>
                Is Everything Powered On?
                <div>
                  <DxRadioGroup
                    :items="yesNoUnsureRadioChoices"
                    :value="null"
                    layout="horizontal"
                    @value-changed="radioBtnChangedPower"
                  />
                </div>
              </div>
              <div style="margin-top: 15px;">
                Are You Getting A Lot Of No Reads?
                <div>
                  <DxRadioGroup
                    :items="yesNoUnsureRadioChoices"
                    :value="null"
                    layout="horizontal"
                    @value-changed="radioBtnChangedNoReads"
                  />
                </div>
              </div>
              <div style="margin-top: 15px;">
                Are You Connected To And Receiving The Correct Data From Your WMS?
                <div>
                  <DxRadioGroup
                    :items="yesNoUnsureRadioChoices"
                    :value="null"
                    layout="horizontal"
                    @value-changed="radioBtnChangedWMS"
                  />
                </div>
              </div>
              <div style="margin-top: 15px;">
                Are You Connected To The PLC?
                <div>
                  <DxRadioGroup
                    :items="yesNoUnsureRadioChoices"
                    :value="null"
                    layout="horizontal"
                    @value-changed="radioBtnChangedPLC"
                  />
                </div>
              </div>
              <div style="margin-top: 15px;">
                Are You Connected To The Scanners, Scales, Pandas, Etc?
                <div>
                  <DxRadioGroup
                    :items="yesNoUnsureRadioChoices"
                    :value="null"
                    layout="horizontal"
                    @value-changed="radioBtnChangedScanScale"
                  />
                </div>
              </div>
              <div style="margin-top: 15px;">
                Do You Believe This Is Related To KOZ And NOT A Controls or Mechanical Issue?
                <div>
                  <DxRadioGroup
                    id="lastRadioBtn"
                    :items="yesNoUnsureRadioChoices"
                    :value="null"
                    layout="horizontal"
                    @value-changed="radioBtnChangedKOZIssue"
                  />
                </div>
              </div>
              <div style="margin-top: 25px;">
                <DxButton
                  text="Submit"
                  type="default"
                  styling-mode="contained"
                  @click="closePreQuestionsPopup"
                  :disabled="!allowClosePreQuestions"
                />
              </div>
            </div>
          </DxPopup>
        </div>
        <div v-if="!ticketDetailsArea">
          <h3 style="background-color: green;" v-if="selectedCustomer != ''">Selected Customer: {{ selectedCustomer }}</h3>
          <h3 style="background-color: crimson;" v-else>Selected Customer: {{ selectedCustomer }}</h3>  
        <div v-if="!hideCustomerSelection">   
        <h3>Step #1: Select A Customer:</h3>
        (Click The Row In The Datagrid To Make Selection)
        <DxDataGrid
          :data-source="customersData"
          :column-hiding-enabled="true"
          :row-alternation-enabled="true"
          :show-borders="true"
          @selection-changed="onCustomerSelectionChanged"
        >
          <DxColumn
            data-field="customer_name"
            caption="Customer"
          />
          <DxColumn
            data-field="headquarters_address"
            caption="Address"
          />
          <DxColumn
            data-field="headquarters_state"
            caption="State"
          />
          <DxColumn
            data-field="headquarters_city"
            caption="City"
          />
          <DxColumn
            data-field="headquarters_zip"
            caption="Zip Code"
          />
          <DxSelection
            :show-check-boxes-mode="always"
            mode="single"
          />
          <DxPaging :page-size="5"/>
          <DxFilterRow
            :visible="true"
          />
        </DxDataGrid>
        </div>
        <div v-else>
          <DxButton
            :width="260"
            text="Unhide Customer Selection"
            type="default"
            styling-mode="contained"
            icon="plus"
            @click="unhideCustomerSelection"
          />
        </div>
        <h3 style="background-color: green;" v-if="selectedSite != ''">Selected Site: {{ selectedSite }}</h3>
        <h3 style="background-color: crimson;" v-else>Selected Site: {{ selectedSite }}</h3>
        <div v-if="siteData.length > 0 && !hideSiteSelection">
        <h3>Step #2: Select A Site: (Specific Job Site The Customer Is Inquiring About)</h3>
        (Click The Row In The Datagrid To Make Selection)
        <DxDataGrid
          :data-source="siteData"
          :column-hiding-enabled="true"
          :row-alternation-enabled="true"
          :show-borders="true"
          @selection-changed="onSiteSelectionChanged"
        >
          <DxColumn
            data-field="site_name"
            caption="Site"
          />
          <DxColumn
            data-field="site_address"
            caption="Address"
          />
          <DxColumn
            data-field="site_state"
            caption="State"
          />
          <DxColumn
            data-field="site_city"
            caption="City"
          />
          <DxColumn
            data-field="site_zip"
            caption="Zip Code"
          />
          <DxSelection
            :show-check-boxes-mode="always"
            mode="single"
          />
          <DxPaging :page-size="5"/>
          <DxFilterRow
            :visible="true"
          />
        </DxDataGrid>
        </div>
        <div v-else>
          <h3 v-if="selectedCustomer != '' && !hideSiteSelection">Customer Has No Sites Set Up In SIM Software 24/7 Database</h3>
          <div v-else-if="hideSiteSelection">
            <DxButton
              :width="260"
              text="Unhide Site Selection"
              type="default"
              styling-mode="contained"
              icon="plus"
              @click="unhideSiteSelection"
            />
          </div>
          <h3 v-else>Select A Customer To Display The Site List Of The Customer</h3>
        </div>


        <h3 style="background-color: green;" v-if="selectedUser != ''">Selected Contact: {{ selectedUser }}</h3>
        <h3 style="background-color: crimson;" v-else>Selected Contact: {{ selectedUser }}</h3>
        <div v-if="userData.length > 0 && !hideUserSelection">
        <h3>Step #3: Select A Primary Contact: (User Who Reached Out To Us, Will Become Primary Contact On The Ticket)</h3>
        (Click The Row In The Datagrid To Make Selection)
        <DxDataGrid
          :data-source="userData"
          :column-hiding-enabled="true"
          :row-alternation-enabled="true"
          :show-borders="true"
          @selection-changed="onUserSelectionChanged"
        >
          <DxColumn
            data-field="customers_first_name"
            caption="First Name"
          />
          <DxColumn
            data-field="customers_last_name"
            caption="Last Name"
          />
          <DxColumn
            data-field="customers_email"
            caption="Email"
          />
          <DxSelection
            :show-check-boxes-mode="always"
            mode="single"
          />
          <DxPaging :page-size="5"/>
          <DxFilterRow
            :visible="true"
          />
        </DxDataGrid>
        
        </div>
        <div v-else>
          <h3 v-if="selectedCustomer != '' && !hideUserSelection">Customer Has No Users Set Up In SIM Software 24/7 Database</h3>
          <div v-else-if="hideUserSelection">
            <DxButton
              :width="260"
              text="Unhide User Selection"
              type="default"
              styling-mode="contained"
              icon="plus"
              @click="unhideUserSelection"
            />
          </div>
          <h3 v-else>Select A Customer To Display The Contact List Of The Customer</h3>
        </div>

        <p style="background-color: rgb(0, 0, 0);">Once you have made all of the selections possible above press the below button to continue<br />IF any of the above selection could not be made leave them unselected and you will be prompted to manually enter the information in the next steps.</p>
      
        <DxButton
          :width="260"
          text="Proceed With Ticket Details"
          type="success"
          styling-mode="contained"
          @click="modifyTicketDetailsArea(true)"
        />
        </div>
        <div v-else>
          <DxButton
            :width="260"
            text="Go Back To Contact Info"
            type="default"
            styling-mode="contained"
            icon="revert"
            @click="modifyTicketDetailsArea(false)"
          />
          <div v-if="isMobile">
            <div class="dx-fieldset">
              <div class="dx-field">
                <div v-if="ticketDetailsKOZCustomer == true" class="dx-field-label">Selected Customer:</div>
                <div v-else class="dx-field-label">Customers Name:</div>
                <div class="dx-field-value">
                  <div v-if="ticketDetailsKOZCustomer == true">
                    <div class="dx-field-label">{{ ticketDetailsCustomer }}</div>
                  </div>
                  <div v-else class="dx-field-label">                 
                    <DxTextBox
                    v-model:value="ticketDetailsCustomer"
                    :disabled="ticketDetailsKOZCustomer"/>
                  </div>  
                </div>
              </div>
              <div class="dx-field">
                <div v-if="ticketDetailsKOZSite == true" class="dx-field-label">Selected Site:</div>
                <div v-else class="dx-field-label">Site Name:</div>
                <div class="dx-field-value">
                  <div v-if="ticketDetailsKOZSite == true">
                    <div class="dx-field-label">{{ ticketDetailsSite }}</div>
                  </div>
                  <div v-else class="dx-field-label">                 
                    <DxTextBox
                      v-model:value="ticketDetailsSite"
                      :disabled="ticketDetailsKOZSite"/>
                  </div>
                </div>
              </div>
              <div class="dx-field">
                <div v-if="ticketDetailsKOZUser == true" class="dx-field-label">Selected Contact:</div>
                <div v-else class="dx-field-label">Primary Contact Email:</div>
                <div class="dx-field-value">
                  <div v-if="ticketDetailsKOZUser == true">
                    <div class="dx-field-label">{{ ticketDetailsUser }}</div>
                  </div>
                  <div v-else class="dx-field-label">                 
                    <DxTextBox
                      v-model:value="ticketDetailsUser"
                      :disabled="ticketDetailsKOZUser"
                      />
                  </div>   
                </div>
              </div>
              <div class="dx-field">
                <div class="dx-field-label">Short Description (Email Subject):</div>
                <div class="dx-field-value">
                  <DxTextBox
                    v-model:value="ticketDetailsSubject"
                  />
                </div>
              </div>
              <div class="dx-field">
                <div class="dx-field-label">Detailed Description (Email Body):</div>
                <div class="dx-field-value">
                  <DxTextArea
                    v-model:value="ticketDetailsBody"
                    :height="200"
                    :max-length="1024"
                  />
                </div>
              </div>
              <div class="dx-field">
                <div class="dx-field-label">
                  <DxButton
                    :width="260"
                    text="Add Email To CC List"
                    type="default"
                    styling-mode="contained"
                    icon="plus"
                    @click="addTmpEmailToCCList"
                  />
                </div>
                <div class="dx-field-value">
                  <DxTextBox
                    v-model:value="ticketDetailsTmpCCEmail"
                  />
                </div>
              </div>
            </div>

            <DxDataGrid
              :key="CCKey"
              :data-source="ticketDetailsCCList"
              style="padding: 10px 0px 10px 0px;"
              :column-hiding-enabled="true"
              :row-alternation-enabled="true"
              :show-borders="true"
            >
              <DxColumn
                data-field="Email"
                caption="CC Emails"
              />
              <DxPaging :page-size="5"/>
            </DxDataGrid>
            <DxButton
              :width="150"
              text="Submit Ticket"
              type="success"
              styling-mode="contained"
              @click="SubmitTicket"
            />

          </div>
          <div v-else style="margin: auto; width: 70%; padding: 10px;">

            <div class="dx-fieldset">
              <div class="dx-field">
                <div v-if="ticketDetailsKOZCustomer == true" class="dx-field-label">Selected Customer:</div>
                <div v-else class="dx-field-label">Customers Name:</div>
                <div class="dx-field-value">
                  <div v-if="ticketDetailsKOZCustomer == true">
                    <div class="dx-field-label">{{ ticketDetailsCustomer }}</div>
                  </div>
                  <div v-else class="dx-field-label">                 
                    <DxTextBox
                    v-model:value="ticketDetailsCustomer"
                    :disabled="ticketDetailsKOZCustomer"/>
                  </div>                  
                </div>
              </div>
              <div class="dx-field">
                <div v-if="ticketDetailsKOZSite == true" class="dx-field-label">Selected Site:</div>
                <div v-else class="dx-field-label">Site Name:</div>
                <div class="dx-field-value">
                  <div v-if="ticketDetailsKOZSite == true">
                    <div class="dx-field-label">{{ ticketDetailsSite }}</div>
                  </div>
                  <div v-else class="dx-field-label">                 
                    <DxTextBox
                      v-model:value="ticketDetailsSite"
                      :disabled="ticketDetailsKOZSite"/>
                  </div>
                </div>
              </div>
              <div class="dx-field">
                <div v-if="ticketDetailsKOZUser == true" class="dx-field-label">Selected Contact:</div>
                <div v-else class="dx-field-label">Primary Contact Email:</div>
                <div class="dx-field-value">
                  <div v-if="ticketDetailsKOZUser == true">
                    <div class="dx-field-label">{{ ticketDetailsUser }}</div>
                  </div>
                  <div v-else class="dx-field-label">                 
                    <DxTextBox
                    v-model:value="ticketDetailsUser"
                    :disabled="ticketDetailsKOZUser"/>
                  </div>                     
                </div>
              </div>
              <div class="dx-field">
                <div class="dx-field-label">Short Description (Email Subject):</div>
                <div class="dx-field-value">
                  <DxTextBox
                    v-model:value="ticketDetailsSubject"
                  />
                </div>
              </div>
              <div class="dx-field">
                <div class="dx-field-label">Initial Questions:</div>
                <div class="dx-field-value">
                  <DxTextArea
                    v-model:value="preTicketQuestions"
                    :height="100"
                    :max-length="1024"
                  />
                </div>
              </div>
              <div class="dx-field">
                <div class="dx-field-label">Detailed Description (Email Body):</div>
                <div class="dx-field-value">
                  <DxTextArea
                    v-model:value="ticketDetailsBody"
                    :height="200"
                    :max-length="1024"
                  />
                </div>
              </div>
              <div class="dx-field">
                <div class="dx-field-label">
                  <DxButton

                    :width="260"
                    text="Add Email To CC List"
                    type="default"
                    styling-mode="contained"
                    icon="plus"
                    @click="addTmpEmailToCCList"
                  />
                </div>
                <div class="dx-field-value">
                  <DxTextBox
                    v-model:value="ticketDetailsTmpCCEmail"
                  />
                </div>
              </div>
            </div>

            <DxDataGrid
              :key="CCKey"
              style="padding-top: 10px; padding-bottom: 10px;"
              :data-source="ticketDetailsCCList"
              :column-hiding-enabled="true"
              :row-alternation-enabled="true"
              :show-borders="true"
            >
              <DxColumn
                data-field="Email"
                caption="CC Emails"
              />
              <DxPaging :page-size="5"/>
            </DxDataGrid>
            <DxButton
              :width="150"
              text="Submit Ticket"
              type="success"
              styling-mode="contained"
              @click="SubmitTicket"
            />

          </div>
          
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import databaseName from '../myFunctions/databaseName';
import axios from 'axios';
import notify from 'devextreme/ui/notify';
import { DxLoadPanel } from 'devextreme-vue/load-panel';
import { useRouter } from 'vue-router';
import auth from "../auth";
import DxButton from 'devextreme-vue/button';
import DxTextBox from 'devextreme-vue/text-box';
import DxTextArea from 'devextreme-vue/text-area';
import DxRadioGroup from 'devextreme-vue/radio-group';
import DxPopup from 'devextreme-vue/popup';
import {
  DxDataGrid,
  DxColumn,
  DxPaging,
  DxSelection,
  DxFilterRow
} from 'devextreme-vue/data-grid';

const router = useRouter();

databaseName.isSomeoneLoggedIn().then(user => {
  console.log(user);
  if (user.data.sim_first_name == null) {
    let sessionStorageUser = sessionStorage.getItem("KOZ_SERVICE_AUTH");
    let sessionStorageJSON = JSON.parse(sessionStorageUser);

    if(sessionStorageJSON != null)
    {
      auth._simUser = sessionStorageJSON.data;
      if(databaseName.validateAuthLevel('create_modify'))
      {
        //put primer stuff in here for like functions to call to get data
        //'any' value passed to function will allow auth for anyuser
        GetCustomerData();
      }
      else
      {
        notify('Your Account Does NOT Have Acces To This Page', 'warning', 10000);
        router.push('/home');
      }
    }
    else
    {
      notify('Please Log In To Use The Service Site', 'warning', 10000);
      router.push('/login-form');
    }
  }
  else
  {
    if(databaseName.validateAuthLevel('create_modify'))
    {
      GetCustomerData();
    }
    else
    {
      notify('Your Account Does NOT Have Acces To This Page', 'warning', 10000);
      router.push('/home');
    }
  }

});

//strip \ ' " ` 

const preTicketQuestionsPopup = ref(false);
const isMobile = ref(false);

databaseName.checkWebsiteVersion();
isMobile.value = databaseName.getScreenSizeSmall();

const loadingVisible = ref(false);

const customersData = ref([]);
const siteData = ref([]);
const selectedCustomer = ref("");
const selectedCustomerEmail = ref("");
const selectedSite = ref("");
const selectedSiteID = ref(0);

const userData = ref([]);
const selectedUser = ref("");
const selectedUsername = ref("");

const hideCustomerSelection = ref(false);
const hideSiteSelection = ref(true);
const hideUserSelection = ref(true);

const ticketDetailsArea = ref(false);

const ticketDetailsKOZCustomer = ref(false);
const ticketDetailsKOZSite = ref(false);
const ticketDetailsKOZUser = ref(false);

const ticketDetailsCustomer = ref("");
const ticketDetailsUser = ref("");
const ticketDetailsUsername = ref("");
const ticketDetailsKOZEmail = ref("");
const ticketDetailsSite = ref("");
const ticketDetailsSiteID = ref(0);
const ticketDetailsSubject = ref("");
const ticketDetailsBody = ref("");

const ticketDetailsTmpCCEmail = ref("");
const ticketDetailsCCList = ref([]);
const CCKey = ref(1);

const validateEmail = (email) => {
  return email.match(
    /\S+@\S+\.\S+/
  );
};

const SubmitTicket = () => {
  let SplitString = [...ticketDetailsBody.value.split('\n'), ...preTicketQuestionsForDetailBody.value];
  let tmpEmailBody = "<p>";
    console.log('here', SplitString);
  if(SplitString.length > 0)
  {
    for(let c = 0; c < SplitString.length; c++)
    {
      SplitString[c] = SplitString[c].replace(/'|"|`|\n/g, "");
      if(c == 0)
      {
        tmpEmailBody += SplitString[c];
      }
      else
      {
        tmpEmailBody += `<br />${SplitString[c]}`;
      }
    }
    tmpEmailBody += "</p>";
  }
  else
  {
    let tmp = ticketDetailsBody.value;
    tmp = tmp.replace(/'|"|`|\n/g, "");
    tmpEmailBody += `${tmp}</p>`;
  }
  console.log('tmp', tmpEmailBody);
  if(ticketDetailsCustomer.value == "")
  {
    notify('Customer Name Cannot Be Blank', 'warning', 7000);
    return;
  }
  if(ticketDetailsSite.value == "")
  {
    notify('Site Name Cannot Be Blank', 'warning', 7000);
    return;
  }
  if(ticketDetailsUser.value == "")
  {
    notify('Primary Contact Email Cannot Be Blank', 'warning', 7000);
    return;
  }
  if(ticketDetailsSubject.value == "")
  {
    notify('Ticket Short Description Cannot Be Blank', 'warning', 7000);
    return;
  }
  if(ticketDetailsBody.value == "")
  {
    notify('Ticket Detailed Description Cannot Be Blank', 'warning', 7000);
    return;
  }

  if(!ticketDetailsKOZUser.value)
  {
    if(!validateEmail(ticketDetailsUser.value))
    {
      notify('Primary Contact Email Is Invalid', 'warning', 7000);
      return;
    }
  }

  auth.getUser().then(user=>{

    let realBody = "<h3>Ticket Submitted Via SIM Software 24/7 Support Website</h3>";
    realBody += "<p>";

    realBody += `Customer: ${ticketDetailsCustomer.value}<br />`;
    realBody += `Site: ${ticketDetailsSite.value}<br />`;
    realBody += `Contact: ${ticketDetailsUser.value}`;

    realBody += "</p>";
    realBody += "<p>You Will See An Email Shortly That The Ticket Has Been Acknowledged By The SIM Software Ticket Monitor<br />After That Email Is Sent A SIM Software Technician Will Respond To Your Ticket Promptly</p>";

    realBody += "<p>Description Of Issue:</p>";
    realBody += tmpEmailBody;

    let primaryContactEmail = "";
    let tmpContact = {
      firstName: "",
      lastName: "",
      email: "",
      phone: ""
    }
    if(ticketDetailsKOZUser.value)
    {
      primaryContactEmail = ticketDetailsKOZEmail.value;
      let nameArr = ticketDetailsUser.value.split(' ');
      if(nameArr.length > 1)
      {
        tmpContact.firstName = nameArr[0];
        tmpContact.lastName = nameArr[1];
      }
      else
      {
        tmpContact.firstName = ticketDetailsUser.value;
      }
      tmpContact.email = ticketDetailsKOZEmail.value;
    }
    else
    {
      primaryContactEmail = ticketDetailsUser.value;
      tmpContact.firstName = "Manual";
      tmpContact.lastName = "Entry";
      tmpContact.email = ticketDetailsUser.value;
    }

    let ccListArr = [];
    for(let c = 0; c < ticketDetailsCCList.value.length; c++)
    {
      ccListArr.push(ticketDetailsCCList.value[c].Email);
    }

    const TicketJSON = {
      zohoDetails:{
        subject: ticketDetailsSubject.value,
        departmentId: "935233000000006907",
        email: primaryContactEmail,
        description: realBody,
        status: "Open",
        channel: "Email",
        contact: tmpContact,
        cf:{
          cf_cc_list: ccListArr
        }
      },
      simswDetails:{
        customer_name: ticketDetailsKOZCustomer.value ? ticketDetailsCustomer.value : null,
        idcustomers_sites: ticketDetailsKOZSite.value ? ticketDetailsSiteID.value : null,
        customer_username: ticketDetailsKOZCustomer.value ? ticketDetailsUsername.value : null,
        created_by: user.data.sim_email
      }
    };

    console.log(TicketJSON);
    loadingVisible.value = true;
    // axios({
    //   method: 'POST',
    //   url: 'api/Ticket/CreateTicket',
    //   data: TicketJSON,
    //   headers:{
    //     'Content-Type': 'application/json',
    //     'Authorization': `Bearer ${user.data.auth_token}`
    //   }
    // })
    // .then(resp=>{
    //   console.log(resp);
    //   notify('Success Creating New Ticket, Redirected To New Page To View', 'success', 5000);
    //   router.push('/view-tickets');
    // })
    // .catch(error=>{
    //   console.error(error);
    //   console.log(error);
    //   if(error.response.status == 401)
    //   {
    //     notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
    //     router.push('/login-form');
    //   }
    //   else if(error.response.status == 500)
    //   {
    //     notify(error.response.data.message, 'error', 10000);
    //   }
    //   else{
    //     notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
    //   }
    // })
    // .finally(()=>{
    //   loadingVisible.value = false;
    // })


  });

}


const addTmpEmailToCCList = () => {
  if(ticketDetailsTmpCCEmail.value != "")
  {
    if(validateEmail(ticketDetailsTmpCCEmail.value))
    {
      ticketDetailsCCList.value.push({Email: ticketDetailsTmpCCEmail.value});
      ticketDetailsTmpCCEmail.value = "";
      CCKey.value++;
    }
    else
    {
      notify('Cannot Add Invalid Email To CC List', 'warning', 7000);
    }
    
  }
  else
  {
    notify('You Cannot Add A Blank User To CC List', 'warning', 5000);
  }
}

const modifyTicketDetailsAreaManually = (flag) => {
  if(flag)
  {
    auth.getUser().then(user => {
      ticketDetailsArea.value = flag;
      ticketDetailsKOZCustomer.value = false;
      ticketDetailsKOZSite.value = false;
      ticketDetailsKOZUser.value = false;

      ticketDetailsCustomer.value = "";
      ticketDetailsUser.value = "";
      ticketDetailsUsername.value = "";
      ticketDetailsSite.value = "";
      ticketDetailsSiteID.value = 0;
      ticketDetailsKOZEmail.value = "";

      ticketDetailsSubject.value = "";
      ticketDetailsBody.value = "";

      ticketDetailsCCList.value = [{Email: user.data.sim_email}];
      ticketDetailsTmpCCEmail.value = "";

      if(selectedCustomer.value != "")
      {
        selectedCustomer.value = ""
        ticketDetailsKOZCustomer.value = false;
        ticketDetailsCustomer.value = "";
      }

      if(selectedSiteID.value != 0)
      {
        selectedSiteID.value = 0;
        ticketDetailsKOZSite.value = false;
        ticketDetailsSite.value = "";
        ticketDetailsSiteID.value = "";
      }

      if(selectedUsername.value != "")
      {
        selectedUsername.value = "";
        ticketDetailsKOZUser.value = false;
        ticketDetailsUser.value = "";
        ticketDetailsUsername.value = "";
        ticketDetailsKOZEmail.value = "";
      }

    })
    
  }
  else
  {
    ticketDetailsArea.value = flag;
  }
}

const modifyTicketDetailsArea = (flag) => {
  
  if(flag)
  {
    auth.getUser().then(user => {
      ticketDetailsArea.value = flag;
      ticketDetailsKOZCustomer.value = false;
      ticketDetailsKOZSite.value = false;
      ticketDetailsKOZUser.value = false;

      ticketDetailsCustomer.value = "";
      ticketDetailsUser.value = "";
      ticketDetailsUsername.value = "";
      ticketDetailsSite.value = "";
      ticketDetailsSiteID.value = 0;
      ticketDetailsKOZEmail.value = "";

      ticketDetailsSubject.value = "";
      ticketDetailsBody.value = "";

      ticketDetailsCCList.value = [{Email: user.data.sim_email}];
      ticketDetailsTmpCCEmail.value = "";

      if(selectedCustomer.value != "")
      {
        ticketDetailsKOZCustomer.value = true;
        ticketDetailsCustomer.value = selectedCustomer.value;
      }

      if(selectedSiteID.value != 0)
      {
        ticketDetailsKOZSite.value = true;
        ticketDetailsSite.value = selectedSite.value;
        ticketDetailsSiteID.value = selectedSiteID.value;
      }

      if(selectedUsername.value != "")
      {
        ticketDetailsKOZUser.value = true;
        ticketDetailsUser.value = selectedUser.value;
        ticketDetailsUsername.value = selectedUsername.value;
        ticketDetailsKOZEmail.value = selectedCustomerEmail.value;
      }
    })
    
  }
  else
  {
    ticketDetailsArea.value = flag;
  }
}

const onCustomerSelectionChanged = (e) =>{
  selectedCustomer.value = e.selectedRowsData[0].customer_name;
  selectedSite.value = "";
  siteData.value = [];
  selectedSiteID.value = 0;
  userData.value = [];
  selectedUser.value = "";
  selectedUsername.value = "";
  selectedCustomerEmail.value = "";
  
  hideCustomerSelection.value = true;

  hideSiteSelection.value = false;
  hideUserSelection.value = false;

  GetSiteData();
  GetUserData();
}
const unhideCustomerSelection = () => {
  hideCustomerSelection.value = false;
}

const onSiteSelectionChanged = (e) => {
  if(e.selectedRowsData.length > 0)
  {
    selectedSite.value = e.selectedRowsData[0].site_name;
    selectedSiteID.value = e.selectedRowsData[0].idcustomers_sites;
    hideSiteSelection.value = true;
  }
}
const unhideSiteSelection = () => {
  hideSiteSelection.value = false;
}

const onUserSelectionChanged = (e) => {
  if(e.selectedRowsData.length > 0)
  {
    selectedUser.value = `${e.selectedRowsData[0].customers_first_name} ${e.selectedRowsData[0].customers_last_name}`;
    selectedUsername.value = e.selectedRowsData[0].customers_username;
    selectedCustomerEmail.value = e.selectedRowsData[0].customers_email;
    hideUserSelection.value = true;
  }
}
const unhideUserSelection = () => {
  hideUserSelection.value = false;
}

const GetSiteData = () => {
  auth.getUser().then(user=>{
    loadingVisible.value = true;
    axios({
      method: 'GET',
      url: `api/Customer/Site?CustomerName=${selectedCustomer.value}`,
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`
      }
    })
    .then(resp=>{
      console.log("GetSiteData=>", resp);
      siteData.value = resp.data.data;
      console.log("SiteData=>", siteData.value);
    })
    .catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify(error.response.data.message, 'error', 10000);
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    })
    .finally(()=>{
      loadingVisible.value = false;
    })
  })
}

const GetUserData = () => {
  auth.getUser().then(user=>{
    loadingVisible.value = true;
    axios({
      method: 'GET',
      url: `api/Auth/CustomerUser?CustomerName=${selectedCustomer.value}`,
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`
      }
    })
    .then(resp=>{
      console.log("GetUserData=>", resp);
      userData.value = resp.data.data;
      console.log("userData=>", userData.value);
    })
    .catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify(error.response.data.message, 'error', 10000);
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    })
    .finally(()=>{
      loadingVisible.value = false;
    })
  })
}

const GetCustomerData = () =>{
  auth.getUser().then(user=>{
    loadingVisible.value = true;
    axios({
      method: 'GET',
      url: 'api/Customer/Customer',
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`
      }
    })
    .then(resp=>{
      console.log("GetCustomerDataResp=>", resp);
      customersData.value = resp.data.data;
      console.log("CustomersData=>", customersData.value);
    })
    .catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify(error.response.data.message, 'error', 10000);
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    })
    .finally(()=>{
      loadingVisible.value = false;
    })
  })
  
}

//GetCustomerData();
//PRE TICKET QUESTIONS
const preTicketQuestionsForDetailBody = ref([]);
const preTicketQuestions = ref('');
const allowClosePreQuestions = ref(false);

const yesNoUnsureRadioChoices = ref([
  'Yes', 'No', 'Unsure'
]);
const radioBtnChangedPower = (data) => {
  console.log('p',data);
  preTicketQuestions.value += `Power: ${data.value}\n`;
};
const radioBtnChangedNoReads = (data) => {
  console.log('noread',data);
  preTicketQuestions.value += `No Reads: ${data.value}\n`;
};
const radioBtnChangedWMS = (data) => {
  console.log('wms',data);
  preTicketQuestions.value += `WMS Connection: ${data.value}\n`;
};
const radioBtnChangedPLC = (data) => {
  console.log('plc',data);
  preTicketQuestions.value += `PLC Connection: ${data.value}\n`;
};
const radioBtnChangedScanScale = (data) => {
  console.log('scan',data);
  preTicketQuestions.value += `Scanners, etc Connection: ${data.value}\n`;
};
const radioBtnChangedKOZIssue = (data) => {
  console.log('KOZIssue',data);
  preTicketQuestions.value += `Related To KOZ: ${data.value}`;
  if (data.element.id == 'lastRadioBtn') {
    allowClosePreQuestions.value = true;
  }
};

const closePreQuestionsPopup = () => {
  preTicketQuestionsForDetailBody.value = preTicketQuestions.value.split('\n')
  console.log(preTicketQuestionsForDetailBody.value);
  preTicketQuestionsPopup.value = false;
  allowClosePreQuestions.value = false;
};

</script>

<style lang="scss">
.disable-events {
  color: yellow;
  background-color: red;
}
.myRule {  
  color: red  
} 
</style>
