<template>
  <div>
    <!-- <h2 class="content-block">View Tickets</h2> -->
    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <DxLoadPanel
          :height="500"
          :width="700"
          :visible="loadingVisible"
          :show-indicator="true"
          :show-pane="true"
          :shading="true"
          shading-color="rgba(0,0,0,0.4)"
        />
        <div v-if="isMobile" style="margin-bottom: 20px;">
          <div style="margin-bottom: 15px;">
            <div>
              User Ticket Filter
            </div>
            <div>
              <DxRadioGroup
                :items="userTicketFilter"
                v-model:value="activeUserTicketFilter"
                layout="horizontal"
                @valueChanged="UserTicketFilterValueChanged"
              />
            </div>
          </div>
          <div>
            <div>
              Status Ticket Filter
            </div>
            <div>
              <DxRadioGroup
                :items="statusTicketFilter"
                v-model:value="activeStatusTicketFilter"
                layout="horizontal"
                @valueChanged="StatusTicketFilterValueChanged"
              />
            </div>
          </div>
        </div>        
        <div v-else style="width: 300px;" class="dx-fieldset">
          <div class="dx-field">
            <div class="dx-field-label">User Ticket Filter</div>
            <div class="dx-field-value">
              <DxRadioGroup
                :items="userTicketFilter"
                v-model:value="activeUserTicketFilter"
                @valueChanged="UserTicketFilterValueChanged"
              />
            </div>
          </div>
          <div class="dx-field">
            <div class="dx-field-label">Status Ticket Filter</div>
            <div class="dx-field-value">
              <DxRadioGroup
                :items="statusTicketFilter"
                v-model:value="activeStatusTicketFilter"
                @valueChanged="StatusTicketFilterValueChanged"
              />
            </div>
          </div>
        </div>
        <div>
          <DxButton
            :width="120"
            text="Refresh"
            type="success"
            styling-mode="contained"
            icon="refresh"
            @click="GetData"
          />
        </div>
        <div>
          <p>If Zoho Button Link Not Available, Click Refresh. May Take Up To One Minute To Appear.</p>
        </div>
        <div>
          <DxDataGrid
            :data-source="dataSource"
            :column-hiding-enabled="true"
            :column-auto-width="true"
            :row-alternation-enabled="true"
            :show-borders="true"
            :word-wrap-enabled="true"
          >
            <DxPaging :page-size="10"/>
            <DxPager
              :show-page-size-selector="true"
              :allowed-page-sizes="[10 , 20, 40]"
              :show-info="true" 
            />
            <DxFilterRow
              :visible="true"
            />
            <DxColumn
              data-field="zoho_ticket_number"
              caption="Ticket #"
              alignment="center"
            />
            <DxColumn
              data-field="customer_name"
              caption="Customer"
            />
            <DxColumn
              data-field="site_name"
              caption="Site"
            />
            <DxColumn
              data-field="zoho_email_created"
              caption="Primary Contact"
            />
            <DxColumn
              data-field="created_datetime"
              caption="Created Date"
              data-type="datetime"
            />
            <DxColumn
              data-field="altered_datetime"
              caption="Altered Date"
              data-type="datetime"
            />
            <DxColumn
              data-field="closed_datetime"
              caption="Closed Date"
              data-type="datetime"
            />
            <DxColumn
              data-field="zoho_subject"
              caption="Ticket Subject"
            />
            <DxColumn
              data-field="zoho_ticket_link"
              caption="Ticket Link"
              cell-template="httpLink"
            />
            <template #httpLink="{data}">
              <div v-if="data.data.zoho_ticket_number != null">
                <DxButton
                  :width="150"
                  text="Open In ZOHO"
                  type="default"
                  styling-mode="contained"
                  @click="openLinkInNewWindow(data.text)"
                />
              </div>
              <div v-else>Waiting On Link</div>
            </template>
          </DxDataGrid>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import databaseName from '../myFunctions/databaseName';
import { useRouter } from 'vue-router';
import auth from "../auth";
import notify from 'devextreme/ui/notify';
import { DxLoadPanel } from 'devextreme-vue/load-panel';
import DxRadioGroup from 'devextreme-vue/radio-group';
import DxButton from 'devextreme-vue/button';
import axios from 'axios';
import {
  DxDataGrid,
  DxColumn,
  DxPaging,
  DxPager,
  DxFilterRow
} from 'devextreme-vue/data-grid';

const router = useRouter();

//const isTechnician = ref(false);

const userTicketFilter = ref(["My Tickets", "All Users Tickets"]);
const statusTicketFilter = ref(["Open Tickets", "All Tickets"]);

const activeUserTicketFilter = ref(userTicketFilter.value[0]);
const activeStatusTicketFilter = ref(statusTicketFilter.value[0]);

const dataSource = ref([]);

const openLinkInNewWindow = (link) => {
  window.open(link, '_blank');
}

databaseName.isSomeoneLoggedIn().then(user => {
  console.log(user);
  if (user.data.sim_first_name == null) {
    let sessionStorageUser = sessionStorage.getItem("KOZ_SERVICE_AUTH");
    let sessionStorageJSON = JSON.parse(sessionStorageUser);

    if(sessionStorageJSON != null)
    {
      auth._simUser = sessionStorageJSON.data;
      if(databaseName.validateAuthLevel('any'))
      {
        //put primer stuff in here for like functions to call to get data
        //'any' value passed to function will allow auth for anyuser
        GetData();
      }
      else
      {
        notify('Your Account Does NOT Have Access To This Page', 'warning', 10000);
        router.push('/home');
      }
    }
    else
    {
      notify('Please Log In To Use The Service Site', 'warning', 10000);
      router.push('/login-form');
    }
  }
  else
  {
    if(databaseName.validateAuthLevel('any'))
    {
      GetData();
    }
    else
    {
      notify('Your Account Does NOT Have Acces To This Page', 'warning', 10000);
      router.push('/home');
    }
  }

});

const isMobile = ref(false);

databaseName.checkWebsiteVersion();
isMobile.value = databaseName.getScreenSizeSmall();

const loadingVisible = ref(false);

const UserTicketFilterValueChanged = () => {
  GetData();
}
const StatusTicketFilterValueChanged = () => {
  GetData();
}

const GetData = () => {
  auth.getUser().then(user=>{

    let timezone = "";
    let closed = false;
    let CreatedBy = null;

    timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    if(activeUserTicketFilter.value == "My Tickets")
    {
      CreatedBy = user.data.sim_email;
    }
    else
    {
      CreatedBy = null;
    }

    if(activeStatusTicketFilter.value == "Open Tickets")
    {
      closed = false;
    }
    else
    {
      closed = true;
    }

    loadingVisible.value = true;
    axios({
      method: 'GET',
      url: `api/Ticket/ViewTickets?Timezone=${timezone}&Closed=${closed}${CreatedBy != null ? `&CreatedBy=${CreatedBy}`: ''}`,
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`
      }
    })
    .then(resp=>{
      dataSource.value = resp.data.data;
    })
    .catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify(error.response.data.message, 'error', 10000);
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    })
    .finally(()=>{
      loadingVisible.value = false;
    })
  })
}

</script>

<style lang="scss">
</style>
