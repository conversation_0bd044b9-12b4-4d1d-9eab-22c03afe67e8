<template>
  <div>
    <DxLoadPanel
      v-model:visible="loadingVisible"
      :show-indicator="true"
      :show-pane="true"
      :shading="true"
      shading-color="rgba(0,0,0,0.4)"
      />
      <DxDataGrid
        style="padding-top: 10px; padding-bottom: 10px;"
        :data-source="statNamesDS"
        :column-hiding-enabled="true"
        :column-auto-width="true"
        :row-alternation-enabled="true"
        :show-borders="true"
        :word-wrap-enabled="true"
        ref="addRowToDataGridRefConnectNamesMasterDetail"
        @row-inserted="OnRowInserted"
        @row-updated="OnRowUpdated"
        @row-removed="OnRowDeleted"
      >
      <DxPaging :page-size="5"/>
      <DxFilterRow
        :visible="true"
      />
      <DxColumn
        data-field="stat_name"
        caption="Stat Name"
      />
      <DxColumn
        data-field="stat_name_disp"
        caption="Stat Display Name"
      />
      <DxEditing
        :allow-updating="true"
        :allow-adding="true"
        :allow-deleting="true"
        mode="row"
      />
      <DxToolbar>
        <DxItem
          location="after"
          template="addRowTemplateConnectNamesMasterDetail"
        />
      </DxToolbar>
        <template #addRowTemplateConnectNamesMasterDetail>
          <DxButton
            text="Add Data Row"
            type="default"
            styling-mode="contained"
            icon="add"
            @click="customAddRowBtnClickConnectNamesMasterDetail"
          />
        </template>
    </DxDataGrid>
  </div>
</template>

<script setup>
import { ref, defineProps } from 'vue';
import databaseName from '../myFunctions/databaseName';
import { useRouter } from 'vue-router';
import axios from 'axios';
import notify from 'devextreme/ui/notify';
import DxLoadPanel from 'devextreme-vue/load-panel';
import auth from "../auth";
import DxButton from 'devextreme-vue/button';
import {
  DxDataGrid,
  DxColumn,
  DxPaging,
  DxFilterRow,
  DxEditing,
  DxToolbar,
  DxItem,
} from 'devextreme-vue/data-grid';

const router = useRouter();

const loadingVisible = ref(false);
const props = defineProps({
  templateData: Object
});

const isMobile = ref(false);
isMobile.value = databaseName.getScreenSizeSmall();

const statNamesDS = ref([]);

const site_id = ref(0);
const connection_id = ref(0);
const stat_group_id = ref(0);


const OnRowInserted = (e) => {
  console.log(e);

  const bodyObj = {
    "stat_name_disp": e.data.stat_name_disp
  };

  auth.getUser().then(user=>{
    loadingVisible.value = true;
    axios({
      method: 'PUT',
      url: `api/WarehouseConnections/StatsNames/${site_id.value}/${connection_id.value}/${stat_group_id.value}/${e.data.stat_name}`,
      data: bodyObj,
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`,
        'Content-Type': 'application/json'
      }
    })
    .then(resp=>{
      notify(resp.data.message, 'success', 5000);
    })
    .catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify(error.response.data.message, 'error', 10000);
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    })
    .finally(()=>{
      loadingVisible.value = false;
      GetStatNameData();
    })
  })
  
}

const OnRowUpdated = (e) => {
  console.log(e);
  const bodyObj = {
    "stat_name": e.data.stat_name,
    "stat_name_disp": e.data.stat_name_disp
  };

  auth.getUser().then(user=>{
    loadingVisible.value = true;
    axios({
      method: 'PATCH',
      url: `api/WarehouseConnections/StatsNames/${site_id.value}/${connection_id.value}/${stat_group_id.value}/${e.data.idconnections_stat_names}`,
      data: bodyObj,
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`,
        'Content-Type': 'application/json'
      }
    })
    .then(resp=>{
      notify(resp.data.message, 'success', 5000);
    })
    .catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify(error.response.data.message, 'error', 10000);
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    })
    .finally(()=>{
      loadingVisible.value = false;
      GetStatNameData();
    })
  })
}

const OnRowDeleted = (e) => {
  console.log(e);

  auth.getUser().then(user=>{
    loadingVisible.value = true;
    axios({
      method: 'DELETE',
      url: `api/WarehouseConnections/StatsNames/${site_id.value}/${connection_id.value}/${stat_group_id.value}/${e.data.idconnections_stat_names}`,
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`,
        'Content-Type': 'application/json'
      }
    })
    .then(resp=>{
      notify(resp.data.message, 'success', 5000);
    })
    .catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify(error.response.data.message, 'error', 10000);
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    })
    .finally(()=>{
      loadingVisible.value = false;
      GetStatNameData();
    })
  })
}

const GetStatNameData = () => {
  auth.getUser().then(user=>{
    site_id.value = props.templateData.data.idcustomers_sites;
    connection_id.value = props.templateData.data.idmonitor_warehouse_connections;
    stat_group_id.value = props.templateData.data.idconnections_stat_groups;
    loadingVisible.value = true;
    axios({
      method: 'GET',
      url: `api/WarehouseConnections/StatsNames/${site_id.value}/${connection_id.value}/${stat_group_id.value}`,
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`
      }
    })
    .then(resp=>{
        statNamesDS.value = resp.data.data;
    })
    .catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify(error.response.data.message, 'error', 10000);
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    })
    .finally(()=>{
      loadingVisible.value = false;
    })
  })
};
GetStatNameData();

const addRowToDataGridRefConnectNamesMasterDetail = ref(null);
const customAddRowBtnClickConnectNamesMasterDetail = () => {
  const dataGrid = addRowToDataGridRefConnectNamesMasterDetail.value.instance;
  dataGrid.addRow();
};
</script>