<template>
  <div
    class="dx-swatch-additional side-navigation-menu"
    @click="forwardClick"
  >
    <slot />
    <div class="menu-container" :class="{ 'compact': compactMode }">
      <dx-tree-view
        ref="treeViewRef"
        :items="items"
        key-expr="path"
        selection-mode="single"
        expanded-expr="isExpanded"
        :focus-state-enabled="false"
        expand-event="click"
        @item-click="handleItemClick"
        width="100%"
      />
    </div>
  </div>
</template>

<script>
import DxTreeView from 'devextreme-vue/tree-view';
import { sizes } from '../utils/media-query';
import navigation from '../app-navigation';
import { onMounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

export default {
  props: {
    compactMode: Boolean
  },
  setup(props, context) {
    const route = useRoute();
    const router = useRouter();

    const isLargeScreen = sizes()['screen-large'];
    const items = navigation.map((item) => {
      if(item.path && !(/^\//.test(item.path))){
        item.path = `/${item.path}`;
      }
      return {...item, expanded: isLargeScreen}
    });

    const treeViewRef = ref(null);

    function forwardClick (...args) {
      context.emit("click", args);
    }

    function handleItemClick(e) {
      if (!e.itemData.path || props.compactMode) {
        return;
      }
      router.push(e.itemData.path);

      const pointerEvent = e.event;
      pointerEvent.stopPropagation();
    }

    function updateSelection () {
      if (!treeViewRef.value || !treeViewRef.value.instance) {
        return;
      }

      treeViewRef.value.instance.selectItem(route.path);
      treeViewRef.value.instance.expandItem(route.path);
    }

    onMounted(() => {
      updateSelection();
      if (props.compactMode) {
        treeViewRef.value.instance.collapseAll();
      }
    });


    watch(
      () => route.path,
      () => {
        updateSelection();
      }
    );

    watch(
      () => props.compactMode,
      () => {
        if (props.compactMode) {
          treeViewRef.value.instance.collapseAll();
        } else {
          updateSelection();
        }
      }
    );

    return {
      treeViewRef,
      items,
      forwardClick,
      handleItemClick,
      updateSelection
    };
  },
  components: {
    DxTreeView
  }
};
</script>

<style scoped lang="scss">
@import "../dx-styles.scss";
@import "../themes/generated/variables.additional.scss";

.side-navigation-menu {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  height: 100%;
  width: 250px !important;

  ::v-deep(.compact .dx-scrollable-content){
    padding: 0px!important;
  
    .dx-treeview-item {
      .dx-icon {
          width: $side-panel-min-width !important;
          margin: 0 !important;
        }
    }
    ::v-deep(.dx-treeview) {
      .dx-treeview-node-container {
        .dx-treeview-node {
          &:not(.dx-state-focused) > .dx-treeview-item.dx-state-hover {
            border-radius: 0px!important;
          }
        }
      }
    }
  }

  .menu-container {
    min-height: 100%;
    display: flex;
    flex: 1;
    // padding: 20px;

    .dx-treeview {
      // ## Long text positioning
      white-space: nowrap;
      // ##
    
      // ## Icon width customization
      ::v-deep(.dx-treeview-item ){
        padding-left: 0;
        padding-right: 0;

        .dx-icon {
          width: 40px; //$side-panel-min-width !important;
          margin: 0;
        }
      }
      // ##

      // ## Arrow customization
      ::v-deep(.dx-treeview-node) {
        padding: 0 0 !important;
      }
      ::v-deep(.dx-scrollable-content) {
        padding: 20px;
      }

      ::v-deep(.dx-treeview-toggle-item-visibility) {
        right: 10px;
        left: auto;
      }

      ::v-deep(.dx-rtl .dx-treeview-toggle-item-visibility) {
        left: 10px;
        right: auto;
      }
      // ##

      // ## Item levels customization
      
      ::v-deep(.dx-treeview-node ){
        color:rgba($base-text-color, 0.75);
        &[aria-level="1"] {
          font-weight: normal;
          color:rgba($base-text-color, 0.75);
          border-bottom: 1px solid rgba($base-border-color, 0);
        }


        &[aria-level="2"]::after {
          content: '';
          display: block;
          height: 100%;
          width: 1px;
          background: $base-accent;
          position: absolute;
          bottom: 0;
          left: 20px;
          right: 0;

        }
        &[aria-level="2"] .dx-treeview-item {
          margin-left: 30px;
          padding-left:11px
        }


        &[aria-level="3"] .dx-treeview-item {
          margin-left: 53px;
          padding-left:10px
        }


        &[aria-level="3"]::after {
          content: '';
          display: block;
          height: 100%;
          width: 1px;
          background: $base-accent;
          position: absolute;
          bottom: 0;
          left: 42px;
          right: 0;

        }
      }
      // ##
    }

    // ## Selected & Focuced items customization
    ::v-deep(.dx-treeview) {
      .dx-treeview-node-container {
        .dx-treeview-node {
          &.dx-state-selected:not(.dx-state-focused) > .dx-treeview-item {
            background: transparent;
          }

          &.dx-state-selected > .dx-treeview-item * {
            color: $base-accent;
          }

          &:not(.dx-state-focused) > .dx-treeview-item.dx-state-hover {
            background-color: lighten($base-bg, 4);
            border-radius: 10px;
          }
        }
      }
    }

    ::v-deep(.dx-theme-generic .dx-treeview) {
      .dx-treeview-node-container
        .dx-treeview-node.dx-state-selected.dx-state-focused
        > .dx-treeview-item
        * {
        color: inherit;
      }
    }
    // ##
  }
}
</style>
