<template>
  <div>
    <!-- <h2 class="content-block">Customer Manager</h2> -->
    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <DxLoadPanel
          :height="500"
          :width="700"
          :visible="loadingVisible"
          :show-indicator="true"
          :show-pane="true"
          :shading="true"
          shading-color="rgba(0,0,0,0.4)"
        />
        <div :class="showMobileViewOrNotCustomerManager.topBtnLayout">
          <div>
            <DxButton
              :width="120"
              text="Refresh"
              type="success"
              styling-mode="contained"
              icon="refresh"
              @click="GetData"
            />
          </div>
          <div :class="showMobileViewOrNotCustomerManager.separateTopBtns">
            <DxButton
              :width="230"
              text="Create New Customer"
              type="default"
              styling-mode="contained"
              icon="plus"
              @click="CreateNewCustomerClicked"
            />
          </div>
        </div>
        <DxDataGrid
          style="padding-top: 10px;"
          :data-source="dataSource"
          :column-hiding-enabled="true"
          :column-auto-width="true"
          :row-alternation-enabled="true"
          :show-borders="true"
          :word-wrap-enabled="true"
          @row-updated="updateCustomer"
        >
          <DxEditing :allow-updating="true" />
          <DxPaging :page-size="10"/>
          <DxPager
            :show-page-size-selector="true"
            :allowed-page-sizes="[10 , 20, 40]"
            :show-info="true" 
          />
          <DxFilterRow
            :visible="true"
          />
          <DxColumn
            data-field="customer_name"
            caption="Customer Name"
          />
          <DxColumn
            data-field="headquarters_address"
            caption="Address"
          />
          <DxColumn
            data-field="headquarters_state"
            caption="State"
          />
          <DxColumn
            data-field="headquarters_city"
            caption="City"
          />
          <DxColumn
            data-field="headquarters_zip"
            caption="Zip"
          />
          <DxMasterDetail
            :enabled="true"
            template="siteDetailTemplate"
          />
          <template #siteDetailTemplate="{data: dataObj}">
            <siteMasterDetailView
              :templateData="dataObj"
            />
          </template>
        </DxDataGrid>
        <DxPopup
          v-model:visible="popupVisible"
          :drag-enabled="false"
          :hide-on-outside-click="false"
          :show-close-button="true"
          :show-title="true"
          height="90%"
          :width="showMobileViewOrNotCustomerManager.popupWidth"
          title="Create Customer"
        >

          <div class="dx-fieldset">
            <div class="dx-field">
              <div class="dx-field-label">Customer Name:</div>
              <div class="dx-field-value">
                <DxTextBox
                  v-model:value="name"
                />
              </div>
            </div>
            <div class="dx-field">
              <div class="dx-field-label">Address:</div>
              <div class="dx-field-value">
                <DxTextBox
                  v-model:value="address"
                />
              </div>
            </div>
            <div class="dx-field">
              <div class="dx-field-label">State:</div>
              <div class="dx-field-value">
                <DxTextBox
                  v-model:value="state"
                />
              </div>
            </div>
            <div class="dx-field">
              <div class="dx-field-label">City:</div>
              <div class="dx-field-value">
                <DxTextBox
                  v-model:value="city"
                />
              </div>
            </div>
            <div class="dx-field">
              <div class="dx-field-label">Zip Code:</div>
              <div class="dx-field-value">
                <DxTextBox
                  v-model:value="zip"
                />
              </div>
            </div>
            <div style="float: right; margin-top: 15px;">
              <DxButton
                :width="170"
                text="Create Customer"
                type="default"
                styling-mode="contained"
                @click="CreateNewCustomer"
              />
            </div>
          </div>
        </DxPopup>
        <!-- <DxPopup v-else
          v-model:visible="popupVisible"
          :drag-enabled="false"
          :hide-on-outside-click="false"
          :show-close-button="true"
          :show-title="true"
          height="90%"
          width="50%"
          title="Create Customer"
        >

          <div class="dx-fieldset">
              <div class="dx-field">
                <div class="dx-field-label">Customer Name:</div>
                <div class="dx-field-value">
                  <DxTextBox
                    v-model:value="name"
                  />
                </div>
              </div>
              <div class="dx-field">
                <div class="dx-field-label">Address:</div>
                <div class="dx-field-value">
                  <DxTextBox
                    v-model:value="address"
                  />
                </div>
              </div>
              <div class="dx-field">
                <div class="dx-field-label">State:</div>
                <div class="dx-field-value">
                  <DxTextBox
                    v-model:value="state"
                  />
                </div>
              </div>
              <div class="dx-field">
                <div class="dx-field-label">City:</div>
                <div class="dx-field-value">
                  <DxTextBox
                    v-model:value="city"
                  />
                </div>
              </div>
              <div class="dx-field">
                <div class="dx-field-label">Zip Code:</div>
                <div class="dx-field-value">
                  <DxTextBox
                    v-model:value="zip"
                  />
                </div>
              </div>
              <div class="dx-field">
                <div class="dx-field-label"></div>
                <div class="dx-field-value">
                  <DxButton
                    :width="170"
                    text="Create Customer"
                    type="default"
                    styling-mode="contained"
                    @click="CreateNewCustomer"
                  />
                </div>
            </div>
          </div>  

        </DxPopup> -->
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import databaseName from '../myFunctions/databaseName';
import { useRouter } from 'vue-router';
import auth from "../auth";
import notify from 'devextreme/ui/notify';
import { DxLoadPanel } from 'devextreme-vue/load-panel';
import axios from 'axios';
import DxButton from 'devextreme-vue/button';
import siteMasterDetailView from './siteMasterDetailView.vue';
import DxPopup from 'devextreme-vue/popup';
import DxTextBox from 'devextreme-vue/text-box';
import {
  DxDataGrid,
  DxColumn,
  DxPaging,
  DxPager,
  DxFilterRow,
  DxMasterDetail,
  DxEditing
} from 'devextreme-vue/data-grid';

const router = useRouter();

const popupVisible = ref(false);

const name = ref("");
const address = ref("");
const state = ref("");
const city = ref("");
const zip = ref("");

databaseName.isSomeoneLoggedIn().then(user => {
  console.log(user);
  if (user.data.sim_first_name == null) {
    let sessionStorageUser = sessionStorage.getItem("KOZ_SERVICE_AUTH");
    let sessionStorageJSON = JSON.parse(sessionStorageUser);

    if(sessionStorageJSON != null)
    {
      auth._simUser = sessionStorageJSON.data;
      if(databaseName.validateAuthLevel('customer_modify'))
      {
        //put primer stuff in here for like functions to call to get data
        //'any' value passed to function will allow auth for anyuser
        GetData();
      }
      else
      {
        notify('Your Account Does NOT Have Acces To This Page', 'warning', 10000);
        router.push('/home');
      }
    }
    else
    {
      notify('Please Log In To Use The Service Site', 'warning', 10000);
      router.push('/login-form');
    }
  }
  else
  {
    if(databaseName.validateAuthLevel('customer_modify'))
    {
      GetData();
    }
    else
    {
      notify('Your Account Does NOT Have Acces To This Page', 'warning', 10000);
      router.push('/home');
    }
  }

});

const dataSource = ref([]);

const isMobile = ref(false);

databaseName.checkWebsiteVersion();
isMobile.value = databaseName.getScreenSizeSmall();

const showMobileViewOrNotFunctionCustomerManager = () => {
  let topBtnLayout;
  let separateTopBtns;
  let popupWidth;
  if (isMobile.value) {
    topBtnLayout = '';
    separateTopBtns = 'separateTopBtnsMobile';
    popupWidth = '95%';
  } else {
    topBtnLayout = 'topBtnLayoutNormal';
    separateTopBtns = '';
    popupWidth = '50%';
  }
  return {
    topBtnLayout,
    separateTopBtns,
    popupWidth,
  }
};
const showMobileViewOrNotCustomerManager = showMobileViewOrNotFunctionCustomerManager();

const loadingVisible = ref(false);

const CreateNewCustomerClicked = () => {
  name.value = "";
  address.value = "";
  state.value = "";
  city.value = "";
  zip.value = "";

  popupVisible.value = true;
}

const updateCustomer = (e) => {
  auth.getUser().then(user=>{
    if(e.data.customer_name == "")
    {
      notify('You Must Include A Customer Name', 'warning', 7000);
      GetData();
      return;
    }
    if(e.data.headquarters_address == "")
    {
      notify('You Must Include An Address', 'warning', 7000);
      GetData();
      return;
    }
    if(e.data.headquarters_state == "")
    {
      notify('You Must Include A State', 'warning', 7000);
      GetData();
      return;
    }
    if(e.data.headquarters_city == "")
    {
      notify('You Must Include A City', 'warning', 7000);
      GetData();
      return;
    }
    if(e.data.headquarters_zip == "")
    {
      notify('You Must Include A Zip Code', 'warning', 7000);
      GetData();
      return;
    }

    axios({
      method: 'PATCH',
      url: 'api/Customer/Customer',
      data: e.data,
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`,
        'Content-Type': 'application/json'
      }
    })
    .then(resp=>{
      //console.log(resp);
      notify(resp.data.message, 'success', 5000);
    })
    .catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify(error.response.data.message, 'error', 10000);
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    }).finally(()=>{
      GetData();
    })

  })
}

const CreateNewCustomer = () => {
  auth.getUser().then(user=>{
    

    if(name.value == "")
    {
      notify('You Must Include A Customer Name', 'warning', 7000);
      return;
    }
    if(address.value == "")
    {
      notify('You Must Include An Address', 'warning', 7000);
      return;
    }
    if(state.value == "")
    {
      notify('You Must Include A State', 'warning', 7000);
      return;
    }
    if(city.value == "")
    {
      notify('You Must Include A City', 'warning', 7000);
      return;
    }
    if(zip.value == "")
    {
      notify('You Must Include A Zip Code', 'warning', 7000);
      return;
    }

    loadingVisible.value = true;

    let CustomerJSON = {
      customer_name: name.value,
      headquarters_address: address.value,
      headquarters_state: state.value,
      headquarters_city: city.value,
      headquarters_zip: zip.value
    };

    axios({
      method: 'POST',
      url: 'api/Customer/Customer',
      data: CustomerJSON,
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`,
        'Content-Type': 'application/json'
      }
    })
    .then(resp=>{
      console.log(resp);
      GetData();
    })
    .catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify(error.response.data.message, 'error', 10000);
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    })
    .finally(()=>{
      loadingVisible.value = false;
      popupVisible.value = false;
    })
  })
}

const GetData = () => {
  auth.getUser().then(user=>{
    loadingVisible.value = true;
    axios({
      method: 'GET',
      url: 'api/Customer/Customer',
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`
      }
    })
    .then(resp=>{
      for(let c = 0; c < resp.data.data.length; c++)
      {
        resp.data.data[c].old_customer_name = resp.data.data[c].customer_name;
      }
      dataSource.value = resp.data.data;
    })
    .catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify(error.response.data.message, 'error', 10000);
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    })
    .finally(()=>{
      loadingVisible.value = false;
    })
  });
}

</script>

<style scoped lang="scss">
.topBtnLayoutNormal {
  display: flex;
  justify-content: space-between;
}
.separateTopBtnsMobile {
  margin-top: 15px;
}
</style>
