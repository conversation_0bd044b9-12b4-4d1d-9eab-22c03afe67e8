<template>
  <div class="container">
    <!-- <h2 class="content-block">Connection/Stat Manager</h2> -->
    <div class="content-block">
      <div class="dx-card responsive-paddings ">
        <DxLoadPanel
          :height="500"
          :width="700"
          :visible="loadingVisible"
          :show-indicator="true"
          :show-pane="true"
          :shading="true"
          shading-color="rgba(0,0,0,0.4)"
        />
        <div v-if="isMobile">
          <div>
            <div style="margin-bottom: 10px;">
              Select Customer
            </div>
            <div>
              <DxSelectBox
                v-model:data-source="customerDS"
                :search-enabled="true"
                display-expr="customer_name"
                value-expr="customer_name"
                search-mode="contains"
                search-expr="customer_name"
                search-timeout="200"
                @value-changed="OnNewCustomerSelected"
              />
            </div>
          </div>
          <div v-if="foundSites" style="margin-top: 25px;">
            <div style="margin-bottom: 10px;">
              Select Site
            </div>
            <div>
              <DxSelectBox
                v-model:data-source="siteDS"
                :search-enabled="true"
                v-model:value="selectedSite"
                display-expr="site_name"
                value-expr="idcustomers_sites"
                search-mode="contains"
                search-expr="site_name"
                search-timeout="200"
                @value-changed="OnNewSiteSelected"
              />
            </div>
          </div>
        </div>
        <!-- <div v-if="isMobile" class="dx-fieldset">
          <div class="dx-field">
            <div class="dx-field-label">Select Customer:</div>
            <div class="dx-field-value">
              <DxSelectBox
                v-model:data-source="customerDS"
                :search-enabled="true"
                display-expr="customer_name"
                value-expr="customer_name"
                search-mode="contains"
                search-expr="customer_name"
                search-timeout="200"
                @value-changed="OnNewCustomerSelected"
              />
            </div>
          </div>
          <div class="dx-field" v-if="foundSites">
            <div class="dx-field-label">Select Site:</div>
            <div class="dx-field-value">
              <DxSelectBox
                v-model:data-source="siteDS"
                :search-enabled="true"
                v-model:value="selectedSite"
                display-expr="site_name"
                value-expr="idcustomers_sites"
                search-mode="contains"
                search-expr="site_name"
                search-timeout="200"
                @value-changed="OnNewSiteSelected"
              />
            </div>
          </div>
        </div> -->
        <div v-else style="width: 50%; margin: 0" class="dx-fieldset">
          <div class="dx-field">
            <div class="dx-field-label">Select Customer:</div>
            <div class="dx-field-value">
              <DxSelectBox
                v-model:data-source="customerDS"
                :search-enabled="true"
                display-expr="customer_name"
                value-expr="customer_name"
                search-mode="contains"
                search-expr="customer_name"
                search-timeout="200"
                @value-changed="OnNewCustomerSelected"
              />
            </div>
          </div>
          <div class="dx-field" v-if="foundSites">
            <div class="dx-field-label">Select Site:</div>
            <div class="dx-field-value">
              <DxSelectBox
                v-model:data-source="siteDS"
                :search-enabled="true"
                v-model:value="selectedSite"
                display-expr="site_name"
                value-expr="idcustomers_sites"
                search-mode="contains"
                search-expr="site_name"
                search-timeout="200"
                @value-changed="OnNewSiteSelected"
              />
            </div>
          </div>
        </div>

        <DxDataGrid
          v-if="selectedSite != 0"
          style="margin-top: 20px"
          v-model:data-source="connectionsDS"
          :column-hiding-enabled="true"
          :row-alternation-enabled="true"
          :show-borders="true"
          ref="addRowToDataGridRef"
          @row-inserted="OnRowInserted"
          @row-updated="OnRowUpdated"
          @row-removed="OnRowDeleted"
        >
          <DxColumn
            data-field="connection_name"
            caption="Connection Name"
          />
          <DxColumn
            data-field="warehouse_connection_type"
            caption="Connection Type"
          >
            <DxLookup
              v-model:data-source="connectionTypesDS"
              value-expr="warehouse_connection_type"
              display-expr="warehouse_connection_type"
            />
          </DxColumn>
          <DxColumn
            data-field="warehouse_connection_protocol_note"
            caption="Connection Protocol Note"
          />
          <DxColumn
            data-field="warehouse_connection_general_note"
            caption="Connection General Note"
          />
          <DxMasterDetail
            :enabled="true"
            template="connectionsGroupsTemplate"
          />
          <template #connectionsGroupsTemplate="{data: dataObj}">
            <ConnectionsGroupsMasterDetailsView
              :templateData="dataObj"
            />
          </template>
          <DxPaging :page-size="10"/>
          <DxPager :allowedPageSizes="[10, 20, 40]" :showInfo="true" :showPageSizeSelector="true" display-mode="compact" />
          <DxFilterRow
            :visible="true"
          />
          <DxEditing
            :allow-updating="true"
            :allow-adding="true"
            :allow-deleting="true"
            mode="row"
          />
          <DxToolbar>
            <DxItem
              location="after"
              template="addRowTemplate"
            />
          </DxToolbar>
            <template #addRowTemplate>
              <DxButton
                text="Add Data Row"
                type="default"
                styling-mode="contained"
                icon="add"
                @click="customAddRowBtnClick"
              />
            </template>
        </DxDataGrid>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import databaseName from '../myFunctions/databaseName';
import { useRouter } from 'vue-router';
import auth from "../auth";
import notify from 'devextreme/ui/notify';
import { DxLoadPanel } from 'devextreme-vue/load-panel';
import axios from 'axios';
import { DxSelectBox } from 'devextreme-vue';
import DxButton from 'devextreme-vue/button';
import {
  DxDataGrid,
  DxColumn,
  DxPaging,
  DxPager,
  DxFilterRow,
  DxMasterDetail,
  DxLookup,
  DxEditing,
  DxToolbar,
  DxItem,
} from 'devextreme-vue/data-grid';
import ConnectionsGroupsMasterDetailsView from './connectionsGroupsMasterDetailsView.vue';

const router = useRouter();
const isMobile = ref(false);
isMobile.value = databaseName.getScreenSizeSmall();

const loadingVisible = ref(false);

const customerDS = ref([]);
const siteDS = ref([]);
const connectionsDS = ref([]);
const connectionTypesDS = ref([]);

const selectedCustomer = ref("");
const selectedSite = ref(0);

const foundSites = ref(false);




databaseName.isSomeoneLoggedIn().then(user => {
  console.log(user);
  if (user.data.sim_first_name == null) {
    let sessionStorageUser = sessionStorage.getItem("KOZ_SERVICE_AUTH");
    let sessionStorageJSON = JSON.parse(sessionStorageUser);

    if(sessionStorageJSON != null)
    {
      auth._simUser = sessionStorageJSON.data;
      if(databaseName.validateAuthLevel('stats_modify'))
      {
        //put primer stuff in here for like functions to call to get data
        //'any' value passed to function will allow auth for anyuser
        GetCustomerData();
      }
      else
      {
        notify('Your Account Does NOT Have Acces To This Page', 'warning', 10000);
        router.push('/home');
      }
    }
    else
    {
      notify('Please Log In To Use The Service Site', 'warning', 10000);
      router.push('/login-form');
    }
  }
  else
  {
    if(databaseName.validateAuthLevel('stats_modify'))
    {
      GetCustomerData();
    }
    else
    {
      notify('Your Account Does NOT Have Acces To This Page', 'warning', 10000);
      router.push('/home');
    }
  }

});

const OnNewCustomerSelected = (e) => {
  //console.log(e);
  selectedSite.value = 0;

  selectedCustomer.value = e.value;
  GetSiteData();
};

const OnNewSiteSelected = (e) => {
  selectedSite.value = e.value;
  console.log(e);

  GetSitesConnectionsData();
};

const addRowToDataGridRef = ref(null);
const customAddRowBtnClick = () => {
  const dataGrid = addRowToDataGridRef.value.instance;
  dataGrid.addRow();
};

const OnRowInserted = (e) => {
  console.log(e);

  const bodyObj = {
    "connection_name": e.data.connection_name,
    "connection_type": e.data.warehouse_connection_type,
    "connection_protocol_note": e.data.warehouse_connection_protocol_note,
    "connection_general_note": e.data.warehouse_connection_general_note
  };

  auth.getUser().then(user=>{
    loadingVisible.value = true;
    axios({
      method: 'PUT',
      url: `api/WarehouseConnections/Connections/${selectedSite.value}/${e.data.connection_name}`,
      data: bodyObj,
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`,
        'Content-Type': 'application/json'
      }
    })
    .then(resp=>{
      notify(resp.data.message, 'success', 5000);
    })
    .catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify(error.response.data.message, 'error', 10000);
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    })
    .finally(()=>{
      loadingVisible.value = false;
      GetSitesConnectionsData();
    })
  })
  
}

const OnRowUpdated = (e) => {
  console.log(e);
  const bodyObj = {
    "connection_name": e.data.connection_name,
    "connection_type": e.data.warehouse_connection_type,
    "connection_protocol_note": e.data.warehouse_connection_protocol_note,
    "connection_general_note": e.data.warehouse_connection_general_note
  };

  auth.getUser().then(user=>{
    loadingVisible.value = true;
    axios({
      method: 'PATCH',
      url: `api/WarehouseConnections/Connections/${selectedSite.value}/${e.data.idmonitor_warehouse_connections}`,
      data: bodyObj,
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`,
        'Content-Type': 'application/json'
      }
    })
    .then(resp=>{
      notify(resp.data.message, 'success', 5000);
    })
    .catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify(error.response.data.message, 'error', 10000);
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    })
    .finally(()=>{
      loadingVisible.value = false;
      GetSitesConnectionsData();
    })
  })
}

const OnRowDeleted = (e) => {
  console.log(e);

  auth.getUser().then(user=>{
    loadingVisible.value = true;
    axios({
      method: 'DELETE',
      url: `api/WarehouseConnections/Connections/${selectedSite.value}/${e.data.idmonitor_warehouse_connections}`,
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`,
        'Content-Type': 'application/json'
      }
    })
    .then(resp=>{
      notify(resp.data.message, 'success', 5000);
    })
    .catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify(error.response.data.message, 'error', 10000);
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    })
    .finally(()=>{
      loadingVisible.value = false;
      GetSitesConnectionsData();
    })
  })
}

const GetConnectionTypes = () => {
  auth.getUser().then(user=>{
    axios({
      method: 'GET',
      url: `api/WarehouseConnections/ConnectionTypes`,
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`
      }
    })
    .then(resp=>{
      connectionTypesDS.value = resp.data.data;
    })
    .catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify(error.response.data.message, 'error', 10000);
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    })
  });
}


const GetSitesConnectionsData = () => {
  GetConnectionTypes();
  auth.getUser().then(user=>{
    loadingVisible.value = true;
    axios({
      method: 'GET',
      url: `api/WarehouseConnections/Connections/${selectedSite.value}`,
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`
      }
    })
    .then(resp=>{
      connectionsDS.value = resp.data.data;
      console.log(resp.data.data);
    })
    .catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify(error.response.data.message, 'error', 10000);
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    })
    .finally(()=>{
      loadingVisible.value = false;
    })
  });
}

const GetCustomerData = () => {
  auth.getUser().then(user=>{
    loadingVisible.value = true;
    axios({
      method: 'GET',
      url: 'api/Customer/Customer',
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`
      }
    })
    .then(resp=>{
      customerDS.value = resp.data.data;
    })
    .catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify(error.response.data.message, 'error', 10000);
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    })
    .finally(()=>{
      loadingVisible.value = false;
    })
  });
}

const GetSiteData = () =>{
  
  auth.getUser().then(user=>{
    axios({
      method: 'GET',
      url: `api/Customer/Site?CustomerName=${selectedCustomer.value}`,
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`
      }
    })
    .then(resp=>{
      foundSites.value = true;
      siteDS.value = resp.data.data;
    })
    .catch(error=>{
      foundSites.value = false;
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify(error.response.data.message, 'error', 10000);
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    })
    .finally(()=>{
      loadingVisible.value = false;
    })
  })
}


</script>

<style scoped lang="scss">
.container {
		display: flex;
		flex-direction: column;
		gap: 20px;
		flex: 1 1 auto;
		overflow-y: auto;
		letter-spacing: 0.3px;
	}
  .dx-field-label {
    width: 120px;
  }
  .dx-field-value {
    width: 200px;
  }
</style>
