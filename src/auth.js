import axios from 'axios';
import databaseName from './myFunctions/databaseName';

const defaultUser = {
  userEmail: 'view only',
  userIdNum: 'view only',
  userFirst: 'View',
  userLast: 'Only',
  userPass: 'password',
  userIdName: 'view only',
  userSecurity: "standard",
  userJwtToken: "1",
  avatarUrl: '',
};

const simUser = {
  sim_first_name: null,
  sim_last_name: null,
  sim_company_name: null,
  sim_office_phone_number: null,
  sim_cell_phone_number: null,
  sim_user_security: null,
  auth_token: null,
  sim_email: null
};


export default {
  _user: defaultUser,
  _simUser: simUser,
  loggedIn() {
    return !!this._user;
  },
/*
  defaultLogIn(){
    this._user = defaultUser;
    return this._user;
  },
  */

  async logIn(email, password) {
    try {
      // let path = databaseName.getPaths();
      // Send request

        const loginClass = {
          "Username": email.toString(),
          // "Password": password.toString(), //call function
          "Password": databaseName.GeneratePasswordHash(password)
        }
        await axios({
          method: 'POST',
          url: 'api/Auth/SIMLogin',
          data: loginClass,
          headers:{
            'Content-Type': 'application/json'
          }
        })
        .then(resp=>{
          // resp.data = JSON.parse(resp.data);
          console.log(resp);

          if(resp.status == 500){
            throw resp.data.message;
          }
          else{
            let sim_first_name = resp.data.data.sim_first_name;
            let sim_last_name = resp.data.data.sim_last_name;
            let sim_company_name = resp.data.data.sim_company_name;
            let sim_office_phone_number = resp.data.data.sim_office_phone_number;
            let sim_cell_phone_number = resp.data.data.sim_cell_phone_number;
            let sim_user_security = resp.data.data.sim_user_security;
            let auth_token = resp.data.data.auth_token;
            let sim_email = resp.data.data.sim_email;

            this._simUser = {...simUser, sim_first_name, sim_last_name, sim_company_name, sim_office_phone_number, sim_cell_phone_number, sim_user_security, auth_token, sim_email}
            

            sessionStorage.setItem("KOZ_SERVICE_AUTH", JSON.stringify({isOk: true, data: this._simUser}));
          }
        })
        .catch(error=>{
          console.log(error);
          if(error.response.status == 500)
          {
            throw error.response.data.message;
          }
          else
          {
            throw "HTTP Error While Trying To Login";
          }
        });
        return{
          isOk: true,
          data: this._simUser
        };
      

    }
    catch(error) {
      return {
        isOk: false,
        message: error
      };
    }
  },

  async logOut() {
    sessionStorage.removeItem("KOZ_SERVICE_AUTH");
    this._simUser = simUser;
  },

  async getUser() {
    try {
      // Send request

      return {
        isOk: true,
        data: this._simUser
      };
    }
    catch {
      return {
        isOk: false
      };
    }
  },
};