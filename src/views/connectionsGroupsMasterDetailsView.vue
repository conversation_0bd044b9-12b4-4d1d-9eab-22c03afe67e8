<template>
  <div>
    <DxLoadPanel
      v-model:visible="loadingVisible"
      :show-indicator="true"
      :show-pane="true"
      :shading="true"
      shading-color="rgba(0,0,0,0.4)"
    />
    <DxDataGrid
      style="padding-top: 10px; padding-bottom: 10px;"
      :data-source="connectionGroupsDS"
      :column-hiding-enabled="true"
      :column-auto-width="true"
      :row-alternation-enabled="true"
      :show-borders="true"
      :word-wrap-enabled="true"
      ref="addRowToDataGridRefConnectGroupsMasterDetail"
      @row-inserted="OnRowInserted"
      @row-updated="OnRowUpdated"
      @row-removed="OnRowDeleted"
    >
      <DxPaging :page-size="5"/>
      <DxFilterRow
        :visible="true"
      />
      <DxColumn
        data-field="stat_group"
        caption="Stat Group"
      />
      <DxMasterDetail
        :enabled="true"
        template="ConnectionsNamesTemplate"
      />
      <template #ConnectionsNamesTemplate="{data: dataObj}">
        <ConnectionsNamesMasterDetailsView
          :templateData="dataObj"
        />
      </template>

      <DxEditing
        :allow-updating="true"
        :allow-adding="true"
        :allow-deleting="true"
        mode="row"
      />
      <DxToolbar>
        <DxItem
          location="after"
          template="addRowTemplateConnectGroupsMasterDetail"
        />
      </DxToolbar>
        <template #addRowTemplateConnectGroupsMasterDetail>
          <DxButton
            text="Add Data Row"
            type="default"
            styling-mode="contained"
            icon="add"
            @click="customAddRowBtnClickConnectGroupsMasterDetail"
          />
        </template>
  </DxDataGrid>
  </div>
</template>

<script setup>
import { ref, defineProps } from 'vue';
import databaseName from '../myFunctions/databaseName';
import { useRouter } from 'vue-router';
import axios from 'axios';
import notify from 'devextreme/ui/notify';
import DxLoadPanel from 'devextreme-vue/load-panel';
import auth from "../auth";
import DxButton from 'devextreme-vue/button';
import {
  DxDataGrid,
  DxColumn,
  DxPaging,
  DxFilterRow,
  DxMasterDetail,
  DxEditing,
  DxToolbar,
  DxItem,
} from 'devextreme-vue/data-grid';
import ConnectionsNamesMasterDetailsView from './connectionsNamesMasterDetailsView.vue';

const router = useRouter();

const loadingVisible = ref(false);
const props = defineProps({
  templateData: Object
});

const isMobile = ref(false);
isMobile.value = databaseName.getScreenSizeSmall();

const connectionGroupsDS = ref([]);

const site_id = ref(0);
const connection_id = ref(0);


const OnRowInserted = (e) => {
  console.log(e);

  auth.getUser().then(user=>{
    loadingVisible.value = true;
    axios({
      method: 'PUT',
      url: `api/WarehouseConnections/StatsGroups/${site_id.value}/${connection_id.value}/${e.data.stat_group}`,
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`,
        'Content-Type': 'application/json'
      }
    })
    .then(resp=>{
      notify(resp.data.message, 'success', 5000);
    })
    .catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify(error.response.data.message, 'error', 10000);
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    })
    .finally(()=>{
      loadingVisible.value = false;
      GetGroupData();
    })
  })
  
}

const OnRowUpdated = (e) => {
  console.log(e);
  const bodyObj = {
    "stat_group": e.data.stat_group
  };

  auth.getUser().then(user=>{
    loadingVisible.value = true;
    axios({
      method: 'PATCH',
      url: `api/WarehouseConnections/StatsGroups/${site_id.value}/${connection_id.value}/${e.data.idconnections_stat_groups}`,
      data: bodyObj,
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`,
        'Content-Type': 'application/json'
      }
    })
    .then(resp=>{
      notify(resp.data.message, 'success', 5000);
    })
    .catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify(error.response.data.message, 'error', 10000);
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    })
    .finally(()=>{
      loadingVisible.value = false;
      GetGroupData();
    })
  })
}

const OnRowDeleted = (e) => {
  console.log(e);

  auth.getUser().then(user=>{
    loadingVisible.value = true;
    axios({
      method: 'DELETE',
      url: `api/WarehouseConnections/StatsGroups/${site_id.value}/${connection_id.value}/${e.data.idconnections_stat_groups}`,
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`,
        'Content-Type': 'application/json'
      }
    })
    .then(resp=>{
      notify(resp.data.message, 'success', 5000);
    })
    .catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify(error.response.data.message, 'error', 10000);
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    })
    .finally(()=>{
      loadingVisible.value = false;
      GetGroupData();
    })
  })
}


const GetGroupData = () => {
  auth.getUser().then(user=>{
    site_id.value = props.templateData.data.idcustomers_sites;
    connection_id.value = props.templateData.data.idmonitor_warehouse_connections;
    loadingVisible.value = true;
    axios({
      method: 'GET',
      url: `api/WarehouseConnections/StatsGroups/${site_id.value}/${connection_id.value}`,
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`
      }
    })
    .then(resp=>{
      connectionGroupsDS.value = resp.data.data;
    })
    .catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify(error.response.data.message, 'error', 10000);
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    })
    .finally(()=>{
      loadingVisible.value = false;
    })
  })
};
GetGroupData();

const addRowToDataGridRefConnectGroupsMasterDetail = ref(null);
const customAddRowBtnClickConnectGroupsMasterDetail = () => {
  const dataGrid = addRowToDataGridRefConnectGroupsMasterDetail.value.instance;
  dataGrid.addRow();
};

</script>