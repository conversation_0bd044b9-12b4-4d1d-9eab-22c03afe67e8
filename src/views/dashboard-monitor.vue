<template>
  <div>
    <!-- <h2 class="content-block">Remote Monitor</h2> -->
    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <DxLoadPanel
          :height="500"
          :width="700"
          :visible="loadingVisible"
          :show-indicator="true"
          :show-pane="true"
          :shading="true"
          shading-color="rgba(0,0,0,0.4)"
        />
        <div class="groupContainerGridTopBtnSelect">
          <DxButton
            style="margin-right: 10px;"
            text="Refresh Page"
            type="success"
            styling-mode="contained"
            icon="refresh"
            @click="getMainDataForDashboard"
          />
          <div style="display: flex; align-items: center;">
            <div style="margin-right: 10px;">
              <strong>Grid Filter:</strong>
            </div>
            <div>
              <DxSelectBox
                :items="statusClearedSelectData"
                v-model:value="statusSelected"
                display-expr="disp"
                value-expr="val"
                @value-changed="filterDataGrid"
                styling-mode="underlined"
                style="float: right;"
                width="150"
              />
            </div>
          </div> 
        </div>
        <div style="display: flex;">
          <div id="mapDiv">
          <!--In the following div the HERE Map will render-->
            <div id="mapContainer" style="height: 600px; width: 115%;" ref="hereMapRef"></div>
          </div>
          <!-- <div id="overGridView" v-if="showMainData"> -->
          <div id="overGridView"><!-- Display the main data for selecting a site to view more details about -->
            <!-- <div class="groupContainerGrid">
              <DxButton
                style="margin-right: 10px;"
                text="Refresh"
                type="success"
                styling-mode="contained"
                icon="refresh"
                @click="getMainDataForDashboard"
              /> 
              <DxSelectBox
                :items="statusClearedSelectData"
                v-model:value="statusSelected"
                display-expr="disp"
                value-expr="val"
                @value-changed="filterDataGrid"
                styling-mode="underlined"
                style="margin-bottom: 5px; border: 1px solid"
                width="150"
              />
            </div> -->

            <DxDataGrid
              style="padding-top: 10px;"
              ref="mainDataGridRef"
              :data-source="mainDataForDash"
              :column-hiding-enabled="false"
              :column-auto-width="true"
              :row-alternation-enabled="true"
              :show-borders="true"
              :word-wrap-enabled="true"
              height="590px"
            >
              <DxScrolling mode="infinite"/>
              <DxFilterRow
                :visible="true"
              />
              <DxColumn
                data-field="site_name"
                caption="Site"
                cell-template="siteStatusTemplate"
                :sort-index="1"
                sort-order="asc"
              />
              <DxColumn
                data-field="koz_tracks_remote_stats"
                caption="KOZ Tracks Site"
                cell-template="showConnectionsTemplate"
                alignment="center"
                :sort-index="0"
                sort-order="desc"
              />
              <DxColumn
                data-field="customer_name"
                caption="Customer"
              />
              <DxColumn
                data-field="formatted_address"
                caption="Address"
              />

              <template #siteStatusTemplate="{data}">
                <div class="groupContainerGrid">
                  <div :class="getStatusColorForSite(data)"></div>
                  <div>{{data.text}}</div>
                </div>
              </template>

              <template #showConnectionsTemplate="{data}">
                <DxButton v-if="data.data.koz_tracks_remote_stats"
                  :width="200"
                  text="View Connections"
                  type="default"
                  styling-mode="contained"
                  @click="ShowSiteConnections(data.data.idcustomers_sites, data.data.site_name)"
                />
                <div v-else>
                  KOZ Not Monitoring
                </div>

              </template>
              <!-- <DxMasterDetail
                :enabled="true"
                template="templateForExtraSiteData"
              />
              <template #templateForExtraSiteData>
                <div style="display: flex; justify-content: space-evenly;">
                  <div>
                    <DxButton
                    text="Audit Log"
                    type="success"
                    styling-mode="contained"
                    @click="clickToOpenLogPopup"
                  />
                  </div>
                  <div>
                    <DxButton
                    text="Error Log"
                    type="default"
                    styling-mode="contained"
                    @click="clickToOpenLogPopup"
                  />
                  </div>
                  <div>
                    <DxButton
                    text="Alarm Log"
                    type="danger"
                    styling-mode="contained"
                    @click="clickToOpenLogPopup"
                  />
                  </div>
                </div>
              </template> -->

            </DxDataGrid>
            <!-- <div style="margin-left: 10px;">
              <div class="keyGroupContainer">
                <div class="keyStatusGood"></div>
                <div>Site Is Tracked By KOZ And All Connections Are Alive</div>
              </div>
              <div class="keyGroupContainer">
                <div class="keyStatusBad"></div>
                <div v-if="isMobile">Site Is Tracked By KOZ And 1 Or More<br/> Connections Are Not Alive</div>
                <div v-else>Site Is Tracked By KOZ And 1 Or More Connections Are Not Alive</div>
              </div>
              <div class="keyGroupContainer">
                <div class="keyStatusNA"></div>
                <div>Site Is NOT Tracked By KOZ, Connection Status Unknown</div>
              </div>
            </div> -->
            <!-- <DxPopup
              v-model:visible="logPopup"
              :drag-enabled="false"
              :hide-on-outside-click="false"
              :show-close-button="true"
              :show-title="true"
              height="80%"
              width="80%"
            > -->
              <!-- <div>
                <h5>Hello, I Will Be Audit Or Error Or Alarm Logs Hopefully In The Future</h5>
                <div> -->
                  <!-- :data-source="group.TimeRanges" -->
                  <!-- <DxDataGrid
                    :height="400"
                    :column-hiding-enabled="true"
                    :row-alternation-enabled="true"
                    :show-borders="true"
                    :word-wrap-enabled="true"
                    :column-auto-width="true"
                    @editor-preparing="modifySearchPanelStyleLogsPopup"
                  >
                    <DxPaging :page-size="10"/>
                    <DxPager
                      :show-page-size-selector="true"
                      :allowed-page-sizes="[10 , 20, 40]"
                      :show-info="true" 
                    /> -->
                    <!-- <DxExport   
                      :enabled="true"
                    /> -->
                    <!-- <DxFilterRow
                      :visible="true"
                    />
                    <DxSearchPanel
                      :visible="true"
                      :width="170"
                      placeholder="Filter Results..."
                    />
                  </DxDataGrid>
                </div>
              </div>

            </DxPopup> -->
            
          </div> <!--End V-If-->
        </div>
        <div style="display: flex; margin-top: 7px;">
          <div class="keyGroupContainer">
            <div class="keyStatusGood"></div>
            <div>Site Is Tracked By KOZ And All Connections Are Alive</div>
          </div>
          <div class="keyGroupContainer">
            <div class="keyStatusBad"></div>
            <div v-if="isMobile">Site Is Tracked By KOZ And 1 Or More<br/> Connections Are Not Alive</div>
            <div v-else>Site Is Tracked By KOZ And 1 Or More Connections Are Not Alive</div>
          </div>
          <div class="keyGroupContainer">
            <div class="keyStatusNA"></div>
            <div>Site Is NOT Tracked By KOZ, Connection Status Unknown (Grid Only)</div>
          </div>
        </div>

        <!--<div v-else>--> <!-- connection data is here in the v-else div -->
        <div v-if="!showMainData" style="margin-top: 25px; border: 1px solid grey; padding: 5px;"> <!-- connection data is here in the v-else div -->
          <div style="display: flex; justify-content: space-between;">
            <DxButton
              :text="`Close ${SiteName}`"
              type="default"
              styling-mode="contained"
              icon="close"
              @click="GoBackToSitesClicked"
            />
            <h4 style="margin-top: 0px;"><strong>{{ SiteName }}</strong></h4>
            <DxButton
              :text="`Refresh ${SiteName}`"
              type="success"
              styling-mode="contained"
              icon="refresh"
              @click="GetSiteConnectionsData"
            />
          </div>
          <h5 style="text-align: center; margin-bottom: 5px;">
            Note: Click Connection to Display Last {{ QueryHours }} Hours of Stats
          </h5>
          <DxDataGrid
            style="padding-top: 10px;"
            :data-source="siteConnectionsDS"
            :column-hiding-enabled="true"
            :column-auto-width="true"
            :row-alternation-enabled="true"
            :show-borders="true"
            :word-wrap-enabled="true"
            height="400px"
            @cell-click="connectionNameClicked"
          >
            <DxScrolling mode="infinite"/>
            <DxFilterRow
              :visible="true"
            />
            <DxColumn
              data-field="connection_name"
              caption="Connection"
              cell-template="siteConnectionTemplate"
            />
            <DxColumn
              data-field="warehouse_connection_type"
              caption="Connection Type"
            />
            <DxColumn
              data-field="warehouse_connection_protocol_note"
              caption="Protocol Note"
            />
            <DxColumn
              data-field="warehouse_connection_general_note"
              caption="General Note"
            />

            <template #siteConnectionTemplate="{data}">
              <div class="groupContainerGrid">
                <div :class="getConnectionStatusColor(data)"></div>
                <div class="scannerGridLink">{{data.text}}</div>
              </div>
            </template>

          </DxDataGrid>
          <h5 style="text-align: center; margin-bottom: 5px;">
            Last 12 Hours Of Scanner Stats For Site:
          </h5>
          <DxDataGrid
            style="padding-top: 10px;"
            :data-source="siteScannerInfoDS"
            :column-hiding-enabled="true"
            :column-auto-width="true"
            :row-alternation-enabled="true"
            :show-borders="true"
            :word-wrap-enabled="true"
            height="400px"
          >
            <DxScrolling mode="infinite"/>
            <DxFilterRow
              :visible="true"
            />
            <DxColumn
              data-field="connection_name"
              caption="Scanner"
            />
            <DxColumn v-for="(val) in siteScannerColumnsDS" :key="val.stat_name"
              :data-field="val.stat_name"
              alignment="center"
            />

          </DxDataGrid>
        </div> <!--End V-Else-->
        <!--Popup for Site Connection Grid and Graph-->
        <DxPopup
          v-model:visible="popupViewConnectionVisible"
          :drag-enabled="false"
          :hide-on-outside-click="false"
          :show-close-button="true"
          :show-title="true"
          height="90%"
          width="90%"
          :title="popupViewConnectionTitle"
        >
          <div :class="showMobileViewOrNotMainMonitor.viewConnectionPopupSearchParams">
            <div :class="showMobileViewOrNotMainMonitor.viewConnectionPopupSingleSearchParams">
              <strong>End Datetime</strong>
              <DxDateBox
                v-model:value="popupStopTime"
                type="datetime"
                styling-mode="underlined"
              />
            </div>
            <div :class="showMobileViewOrNotMainMonitor.viewConnectionPopupSingleSearchParams">
              <strong>Past Hours</strong>
              <DxNumberBox
                :max="24"
                :min="1"
                :show-spin-buttons="true"
                v-model:value="QueryHours"
                styling-mode="underlined"
              />
            </div>
            <div :class="showMobileViewOrNotMainMonitor.viewConnectionPopupSingleSearchParams">
              <DxButton
                :width="150"
                text="Search"
                type="default"
                styling-mode="contained"
                @click="ReFilterButtonPressed"
              />
            </div>
          </div>
          <DxTabPanel
            :key="popupSelectionChanged"
            :data-source="popupViewConnectionDS.stat_groups"
            v-model:selected-index="popupGroupIndex"
            :loop="true"
            :animation-enabled="false"
            :swipe-enabled="false"
          >
            <template #title="{ data: group }">
              <span>{{ group.stat_group }}</span>
            </template>
            <template #item="{ data: group }">
              <div>
                <DxDataGrid
                  :height="300"
                  :data-source="group.TimeRanges"
                  :column-hiding-enabled="showMobileViewOrNotMainMonitor.popupTimeRangeGridColumnHide"
                  :row-alternation-enabled="true"
                  :show-borders="true"
                  :word-wrap-enabled="true"
                  :column-auto-width="true"
                >
                  <DxColumn
                    :width="showMobileViewOrNotMainMonitor.popupGridTimeRangeColumnsWidth"
                    data-field="StartDatetime"
                    caption="Start"
                    data-type="datetime"
                    :fixed="true" 
                    fixed-position="left"
                  />
                  <DxColumn
                    :width="showMobileViewOrNotMainMonitor.popupGridTimeRangeColumnsWidth"
                    data-field="EndDatetime"
                    caption="End"
                    data-type="datetime"
                    :fixed="true" 
                    fixed-position="left"
                  />
                  <DxColumn v-for="(val) in group.stat_columns" :key="val.stat_name"
                    :data-field="val.stat_name"
                    :caption="val.stat_name_disp"
                    alignemnt="center"
                  />
                  <DxSummary>
                    <DxTotalItem v-for="(val) in group.stat_columns" :key="val.stat_name"
                      :column="val.stat_name"
                      summary-type="sum"
                      display-format="{0}"
                      css-class="gridTotals"
                    />
                  </DxSummary>
                </DxDataGrid>
                <DxChart
                  :data-source="group.TimeRanges"
                  title="Group Stats"
                >
                  <DxTooltip
                    :enabled="true"
                    :content-template="graphToolTipFunctionRecentScans"
                    :z-index="2000"
                  />
                  <DxCommonSeriesSettings
                    argument-field="StartDatetime"
                    type="line"
                    hover-mode="allArgumentPoints"
                  />
                  <DxArgumentAxis
                    argument-type="datetime"
                  >
                    <DxLabel
                      :staggering-spacing="10"
                      display-mode="stagger"
                    />
                  </DxArgumentAxis>
                  <DxSeries v-for="(val) in group.stat_columns" :key="val.stat_name"
                    :value-field="val.stat_name"
                    :name="val.stat_name_disp"
                  />
                  <DxLegend 
                    vertical-alignment="bottom"
                    horizontal-alignment="center"
                  >
                    <DxMargin :top="25"/>
                  </DxLegend>
                </DxChart>
              </div>
            </template>
          </DxTabPanel>
        </DxPopup>

      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import axios from 'axios';
import { API_KEY } from '../myFunctions/hereMaps-api-key.js';
import databaseName from '../myFunctions/databaseName';
import { useRouter } from 'vue-router';
import auth from "../auth";
import notify from 'devextreme/ui/notify';
import { DxLoadPanel } from 'devextreme-vue/load-panel';
import DxButton from 'devextreme-vue/button';
import DxPopup from 'devextreme-vue/popup';
import { DxSelectBox } from 'devextreme-vue/select-box';
import DxTabPanel from 'devextreme-vue/tab-panel';
import { DxNumberBox } from 'devextreme-vue/number-box';
import DxDateBox from 'devextreme-vue/date-box';
import {
  DxDataGrid,
  DxColumn,
  DxScrolling,
  DxFilterRow,
  DxSummary,
  DxTotalItem,
  //DxMasterDetail,
  //DxPager,
  //DxPaging,
  //DxExport,
  //DxSearchPanel,
} from 'devextreme-vue/data-grid';
import DxChart, {
    DxSeries,
    DxCommonSeriesSettings,
    DxTooltip,
    DxLabel,
    DxMargin,
    DxLegend,
    DxArgumentAxis,
} from 'devextreme-vue/chart';


const loadingVisible = ref(false);
const router = useRouter();

// HERE MAP 
const platform = ref(null);
const hereMapRef = ref(null);
onMounted(() => {
  platform.value = new window.H.service.Platform({
    apikey: API_KEY
  });
  getMainDataForDashboard();
  initializeHereMap();
});

const H = window.H;
let map;
let ui;
const initializeHereMap = () => { //rendering map
  const mapContainer = document.getElementById('mapContainer');
  // Obtain the default map types from the platform object
  var maptypes = platform.value.createDefaultLayers();

  // Instantiate (and display) a map object:
  map = new H.Map(mapContainer, maptypes.vector.normal.map, {
    zoom: 4.1,
    center: { lat: 39.50, lng: -95.50 }
  });

  addEventListener("resize", () => map.getViewPort().resize());

  // add behavior control
  new H.mapevents.Behavior(new H.mapevents.MapEvents(map));

  // add UI
  ui = H.ui.UI.createDefault(map, maptypes);
  //Hiding Different Map Views We Don't Need - traffic, satellite, etc.
  let mapsettings = ui.getControl('mapsettings');
  mapsettings.setVisibility(false);
};
// End rendering the initial map

// MARKERS
const makingMarkersForMap = () => {
  const group = new H.map.Group();
  var bubble;
  map.addObject(group);
  //HOVER EVENT TO SEE SITE INFO
  group.addEventListener('pointerenter', (evt) => {  
    //closes open bubbles
    ui.getBubbles().forEach(bub => ui.removeBubble(bub));    
    bubble = new H.ui.InfoBubble(evt.target.getGeometry(), {
      // read custom data
      content: evt.target.getData().disp,
    });
    // Open Bubble coordinates to try and prevent bubble from covering marker and preventing click event
    bubble.setPosition(map.screenToGeo(
      evt.currentPointer.viewportX,
      evt.currentPointer.viewportY
    ));
    // show info bubble
    ui.addBubble(bubble);
  }, true);

  // CLICK TO OPEN SITE DATA
  group.addEventListener('tap', (evt) => {
    for (let d = 0; d < mainDataForDash.value.length; d++) {
      if (evt.target.data.id == mainDataForDash.value[d].idcustomers_sites) {
        ShowSiteConnections(mainDataForDash.value[d].idcustomers_sites, mainDataForDash.value[d].site_name);
      }
    }
    ui.getBubbles().forEach(bub => ui.removeBubble(bub));
  });

  //CREATE MARKERS
  for (let m = 0; m < mainDataForDash.value.length; m++) {
    if (mainDataForDash.value[m].koz_tracks_remote_stats) {
      if (mainDataForDash.value[m].koz_reported_problem) {
        let svgMarker = '<svg xmlns="http://www.w3.org/2000/svg" ' +
        `id="${mainDataForDash.value[m].idcustomers_sites}" x="0px" y="0px" width="32" height="32" viewBox="0 0 263.335 263.335" style="enable-background:new 0 0 263.335 263.335;" xml:space="preserve"> ` +
        '<path d="M40.479,159.021c21.032,39.992,49.879,74.22,85.732,101.756c0.656,0.747,1.473,1.382,2.394,1.839 ' +
        'c0.838-0.396,1.57-0.962,2.178-1.647c80.218-61.433,95.861-125.824,96.44-128.34c2.366-9.017,3.57-18.055,3.57-26.864 ' +
        'C237.389,47.429,189.957,0,131.665,0C73.369,0,25.946,47.424,25.946,105.723c0,8.636,1.148,17.469,3.412,26.28" fill="#FF0000" stroke="black" stroke-width="5"/></svg>'
        const mapIconBad = new H.map.Icon(svgMarker);
        var markerBad = new H.map.Marker({ lat: mainDataForDash.value[m].lat_coordinate, lng: mainDataForDash.value[m].lng_coordinate }, { icon: mapIconBad });
        markerBad.setData({
                            disp: `<div>${mainDataForDash.value[m].customer_name}</div>` +
                                  `<div>${mainDataForDash.value[m].site_name}</div>`,
                            id: mainDataForDash.value[m].idcustomers_sites
                          });
        group.addObject(markerBad);
      } else {
        let svgMarker = '<svg xmlns="http://www.w3.org/2000/svg" ' +
        `id="${mainDataForDash.value[m].idcustomers_sites}" x="0px" y="0px" width="32" height="32" viewBox="0 0 263.335 263.335" style="enable-background:new 0 0 263.335 263.335;" xml:space="preserve"> ` +
        '<path d="M40.479,159.021c21.032,39.992,49.879,74.22,85.732,101.756c0.656,0.747,1.473,1.382,2.394,1.839 ' +
        'c0.838-0.396,1.57-0.962,2.178-1.647c80.218-61.433,95.861-125.824,96.44-128.34c2.366-9.017,3.57-18.055,3.57-26.864 ' +
        'C237.389,47.429,189.957,0,131.665,0C73.369,0,25.946,47.424,25.946,105.723c0,8.636,1.148,17.469,3.412,26.28" fill="#90EE90" stroke="black" stroke-width="5"/></svg>'
        const mapIconGood = new H.map.Icon(svgMarker);
        var markerGood = new H.map.Marker({ lat: mainDataForDash.value[m].lat_coordinate, lng: mainDataForDash.value[m].lng_coordinate }, { icon: mapIconGood });
        markerGood.setData({
                            disp: `<div>${mainDataForDash.value[m].customer_name}</div>` +
                                  `<div>${mainDataForDash.value[m].site_name}</div>`,
                            id: mainDataForDash.value[m].idcustomers_sites
                          });
        group.addObject(markerGood);
      }
    }
  }
  loadingVisible.value = false;
};
// END HERE MAP

//date type entension to format the dates into a string that we care about
Date.prototype.FormatDatetime = function() {
  let date = this.getFullYear()+'-'+((this.getMonth()+1).toString().padStart(2, '0'))+'-'+this.getDate().toString().padStart(2, '0');
  let time = this.getHours().toString().padStart(2, '0') + ":" + this.getMinutes().toString().padStart(2, '0') + ':' + this.getSeconds().toString().padStart(2, '0');
  return date + ' ' + time;
}

//Popup Data
const popupSelectedConnectionid = ref(0);
const popupViewConnectionVisible = ref(false);
const popupViewConnectionDS = ref({});
const popupViewConnectionTitle = ref("");
const popupStartTimeString = ref("");
const popupStopTimeString = ref("");
const popupStopTime = ref(new Date());
const popupReturnDataInterval = ref(30);
const popupSelectionChanged = ref(0);
const popupGroupIndex = ref(0);
//End Popup Data

const selected_site_id = ref(0);
const SiteName = ref("Sites");
const QueryHours = ref(4);

const siteConnectionsDS = ref([]);
const siteScannerInfoDS = ref([]);
const siteScannerColumnsDS = ref([]);

const mainDataForDash = ref([]);
const arrayForGeoMarks = ref([]);
const showMainData = ref(true);
const getMainDataForDashboard = () => {
  auth.getUser().then(user=>{
    loadingVisible.value = true;
    arrayForGeoMarks.value = [];

    console.log(user)
    axios({
      method: 'GET',
      url: `api/RemoteStats/OverallScreen`,
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`
      }
    })
    .then(resp=>{
      // initializeHereMap();
      console.log(resp.data.data)
      mainDataForDash.value = resp.data.data;
      
      // console.log(mainDataForDash.value);
      // Getting Marker Map coordinates from DB
      makingMarkersForMap();
    }).catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify(error.response.data.message, 'error', 10000);
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    })
    // .finally(()=>{
    //   loadingVisible.value = false;
    // })
  })
}
//getMainDataForDashboard();

const getStatusColorForSite = (gridData) => {
  if(gridData.data.koz_tracks_remote_stats) {
    if(gridData.data.koz_reported_problem) {
      return 'statusBad';
    } else {
      return 'statusGood';
    }
  } else {
    return 'statusNA';
  }
};
const getConnectionStatusColor = (data) => {
  if(data.data.warehouse_connection_heartbeat_in_range) {
    if(data.data.warehouse_connection_alive || data.data.warehouse_connection_type == "SERVICE") {
      return 'statusGood';
    } else {
      return 'statusBad';
    }
  } else {
    return 'statusBad';
  }
};
const ShowSiteConnections = (site_id, site_name) => {
  selected_site_id.value = site_id;
  siteConnectionsDS.value = [];
  siteScannerInfoDS.value = [];
  siteScannerColumnsDS.value = [];
  showMainData.value = false;
  SiteName.value = site_name;

  GetSiteConnectionsData();
}
const GetSiteConnectionsData = () => {
  auth.getUser().then(user=>{
    loadingVisible.value = true;

    axios({
      method: 'GET',
      url: `api/RemoteStats/OverallScreen/${selected_site_id.value}`,
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`
      }
    })
    .then(resp=>{
      console.log(resp.data.data);
      siteConnectionsDS.value = resp.data.data.WarehouseConnectionData;
      siteScannerColumnsDS.value = resp.data.data.ScannerColumns;

      let scannerColumns = {};

      //dynamically create all the data columns based on SCANNER information passed by API
      for(let c = 0; c < resp.data.data.ScannerColumns.length; c++)
      {
        scannerColumns[resp.data.data.ScannerColumns[c].stat_name] = 0;
      }

      let tmpScannerDataHolder = [];
      //dynamically create a list of all the scanners and add the columns from the above step to them
      for(let c = 0; c < resp.data.data.WarehouseConnectionData.length; c++)
      {
        if(resp.data.data.WarehouseConnectionData[c].warehouse_connection_type == 'SCANNER')
        {
          tmpScannerDataHolder.push(JSON.parse(JSON.stringify(scannerColumns)));
          tmpScannerDataHolder[tmpScannerDataHolder.length - 1].connection_name = resp.data.data.WarehouseConnectionData[c].connection_name;
          
        }
      }
      //dynamically adjust the counts for the scanners conditions
      for(let c = 0; c < resp.data.data.ScannerStats.length; c++)
      {
        if(resp.data.data.ScannerStats[c].connection_name == resp.data.data.ScannerStats[c].stat_group)
        {
          let tmpIndex = tmpScannerDataHolder.findIndex(p => p.connection_name == resp.data.data.ScannerStats[c].connection_name);
          if(tmpIndex != -1)
          {
            tmpScannerDataHolder[tmpIndex][resp.data.data.ScannerStats[c].stat_name] += resp.data.data.ScannerStats[c].sum;
          }
        }
      }
      siteScannerInfoDS.value = tmpScannerDataHolder;

    }).catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify(error.response.data.message, 'error', 10000);
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    })
    .finally(()=>{
      loadingVisible.value = false;
    })
  })
};
const GoBackToSitesClicked = () => {
  showMainData.value = true;
  SiteName.value = "Sites";
  getMainDataForDashboard();
};

const connectionNameClicked = (e) => {
  if(e.columnIndex == 0)
  {
    //console.log(e);
    popupSelectedConnectionid.value = e.key.idmonitor_warehouse_connections;

    popupViewConnectionTitle.value = `Connection: ${e.key.connection_name} Individual Stats`;
    popupReturnDataInterval.value = 30; 
    let tmpDate = new Date();
    popupStopTime.value = new Date();
    QueryHours.value = 4;
    tmpDate.setHours(tmpDate.getHours() - QueryHours.value);   
    popupStartTimeString.value = tmpDate.FormatDatetime();
    popupStopTimeString.value = new Date().FormatDatetime();
    popupGroupIndex.value = 0;

    getConnectionPopupData();
  }
};
const ReFilterButtonPressed = () => {
  let tmpDate = new Date(popupStopTime.value.FormatDatetime());
  //tmpDate = popupStopTime.value;
  tmpDate.setHours(tmpDate.getHours() - QueryHours.value);
  popupStartTimeString.value = tmpDate.FormatDatetime();

  popupStopTimeString.value = popupStopTime.value.FormatDatetime();

  getConnectionPopupData();
};

const getConnectionPopupData = () => {
  auth.getUser().then(user=>{
    loadingVisible.value = true;

    //max time span is 24 hours
    //intervals are 15, 30, and 60 minutes
    const Params = {
      Timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      StartDatetime: popupStartTimeString.value,
      EndDatetime: popupStopTimeString.value,
      IntervalMins: popupReturnDataInterval.value
    };

    axios({
      method: 'GET',
      url: `api/RemoteStats/OverallScreen/${selected_site_id.value}/${popupSelectedConnectionid.value}`,
      params: Params,
      headers:{
        'Authorization': `Bearer ${user.data.auth_token}`
      }
    })
    .then(resp=>{
      console.log(resp.data.data);

      for(let c = 0; c < resp.data.data.stat_groups.length; c++)
      {
        for(let i = 0; i < resp.data.data.stat_groups[c].TimeRanges.length; i++)
        {
          for(let tr = 0; tr < resp.data.data.stat_groups[c].stat_columns.length; tr++)
          {
            resp.data.data.stat_groups[c].TimeRanges[i][resp.data.data.stat_groups[c].stat_columns[tr].stat_name] = 0;
          }

          for(let sd = 0; sd < resp.data.data.stat_groups[c].TimeRanges[i].stat_names.length; sd++)
          {
            resp.data.data.stat_groups[c].TimeRanges[i][resp.data.data.stat_groups[c].TimeRanges[i].stat_names[sd].stat_name] = resp.data.data.stat_groups[c].TimeRanges[i].stat_names[sd].stat_count;
          }

        }
      }

      popupViewConnectionDS.value = resp.data.data;
      console.log(popupViewConnectionDS.value)

      popupViewConnectionVisible.value = true;
    })
    .catch(error=>{
      console.error(error);
      console.log(error);
      if(error.response.status == 401)
      {
        notify('Your Accounts Access Token Has Expired Please Log Back In To Get A New Token', 'warning', 10000);
        router.push('/login-form');
      }
      else if(error.response.status == 500)
      {
        notify("No Data Found In Selected Time Frame", 'warning', 10000);
        popupViewConnectionDS.value = [];
        popupViewConnectionVisible.value = true;
      }
      else{
        notify('Unhandled HTTP Exception Has Occured', 'error', 10000);
      }
    })
    .finally(()=>{
      loadingVisible.value = false;
    })
  })
};
const graphToolTipFunctionRecentScans = (data) => {
  return `${data.seriesName}: ${data.value}`;
};

// Filter Main Grid By What Is Monitored
const mainDataGridRef = ref(null);
const filterDataGrid = () => {
  const dataGrid = mainDataGridRef.value.instance;
  if (statusSelected.value == 'monitor') {
    dataGrid.filter([
      ['koz_tracks_remote_stats', '=', true]
    ])
  } else if (statusSelected.value == 'nope') {
    dataGrid.filter([
      ['koz_tracks_remote_stats', '=', false]
    ])
  } else {
    dataGrid.clearFilter();
  }
};

const statusClearedSelectData = ref([
  { disp: "Show All", val: "all" },
  { disp: "Monitoring", val: "monitor" },
  { disp: "Not Monitoring", val: "nope" }
]);

const statusSelected = ref(statusClearedSelectData.value[0].val);
// End Filter Main Grid

//Mobile Or Normal View Styling
const isMobile = ref(false);
isMobile.value = databaseName.getScreenSizeSmall();
const showMobileViewOrNotFunctionMainMonitor = () => {
  let viewConnectionPopupSearchParams = '';
  let viewConnectionPopupSingleSearchParams = '';
  let popupTimeRangeGridColumnHide = '';
  let popupGridTimeRangeColumnsWidth = '';
  if (isMobile.value) {
    viewConnectionPopupSearchParams = '';
    viewConnectionPopupSingleSearchParams = 'viewConnectionPopupSingleSearchParamsMobile';
    popupTimeRangeGridColumnHide = true;
    popupGridTimeRangeColumnsWidth = 'auto';
  } else {
    viewConnectionPopupSearchParams = 'viewConnectionPopupSearchParamsNormal';
    viewConnectionPopupSingleSearchParams = 'viewConnectionPopupSingleSearchParamsNormal';
    popupTimeRangeGridColumnHide = false;
    popupGridTimeRangeColumnsWidth = 170;
  }
  return {
    viewConnectionPopupSearchParams,
    viewConnectionPopupSingleSearchParams,
    popupTimeRangeGridColumnHide,
    popupGridTimeRangeColumnsWidth,
  }
};
const showMobileViewOrNotMainMonitor = showMobileViewOrNotFunctionMainMonitor();
</script>

<style lang="scss" scoped>
#mapDiv {
  width: 100vw;
  min-width: 360px;
  width: 50%;
  overflow: hidden;
  //text-align: center;
  // margin: auto;
  margin: 2px;
  background-color: #ccc;
  // border: 1px solid grey;
}
#overGridView {
  width: 50%;
  border: 1px solid grey;
  margin: 2px;
}
.groupContainerGridTopBtnSelect {
  display: flex;
  justify-content: space-between;
  margin-bottom: 7px;
}
.keyGroupContainer {
  display: flex;
  //margin-top: 7px;
  align-items: center;
  margin-right: 10px;
}
.keyStatusGood {
  background-color: #90EE90;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  margin-right: 5px;
}
.keyStatusBad {
  background-color: #FF0000;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  margin-right: 5px;
}
.keyStatusNA {
  background-color: #4f4f4f;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  margin-right: 5px;
}
.groupContainerGrid {
  display: flex;
  align-items: center;
}
.statusGood {
  background-color: #90EE90;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 10px;
}
.statusBad {
  background-color: #FF0000;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 10px;
}
.statusNA {
  background-color: #4f4f4f;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 10px;
}
.scannerGridLink {
  cursor: pointer;
  color: lightgray;
  text-decoration: underline;
}
.gridTotals {
  color: #09b0d2!important;
  text-decoration: underline;
}
.viewConnectionPopupSearchParamsNormal {
  display: flex;
  align-items: flex-end;
  margin-bottom: 35px;
}
.viewConnectionPopupSingleSearchParamsNormal {
  margin-right: 25px;
}
.viewConnectionPopupSingleSearchParamsMobile {
  margin-bottom: 15px;
}
</style>
