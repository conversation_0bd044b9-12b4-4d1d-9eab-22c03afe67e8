<template>
  <div class="stat-box-group">
    <!-- Render each stat box in the group -->
    <div v-for="item in widgetData.items" :key="item.id" :class="['stat-box-container', `stat-box-${item.id}`]" :style="getItemStyle(item)" >
      <stat-box-widget 
        :widget-data="getStatBoxData(item)" 
        :stat-value="getStatValue(item)"
				:data-type="item.dataType"
				:format="item.format"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, defineProps } from 'vue';
import { useRestAPI } from '@/composables/useRestAPI';
import StatBoxWidget from '@/components/dashboard/stat-box-widget.vue';

const props = defineProps({
  widgetData: {
    type: Object,
    required: true
  }
});

const api = useRestAPI();
const loading = ref(true);
const error = ref(null);
const statsData = ref(null);

// Fetch data for all stats in the group
const fetchGroupData = async () => {
  if (!props.widgetData.dataSource) {
    error.value = 'No data source specified for stat group';
    loading.value = false;
    return;
  }

  loading.value = true;
  error.value = null;

  try {
    // For demo purposes, use mock data
    if (props.widgetData.dataSource === '/api/stats') {
      // Mock data for demo
      statsData.value = {
        openOrders: { value: 22 },
        inProgressOrders: { value: 15 },
        completedOrders: { value: 105 },
        avgProcessingTime: { value: 32 }
      };
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
    } else {
      // Real API call
      const response = await api.get(props.widgetData.dataSource);
      statsData.value = response.data;
    }
  } catch (err) {
    console.error('Error fetching stat group data:', err);
    error.value = 'Failed to load statistics';
  } finally {
    loading.value = false;
  }
};

// Process the widget item to create a proper widget data object for the stat-box component
const getStatBoxData = (item) => {
  return {
    ...item,
    // Don't pass the dataSource as each stat box doesn't need to make its own API call
    dataSource: undefined
  };
};

// Get the value for a specific stat box
const getStatValue = (item) => {
  if (!statsData.value) return null;
  
  // Find the data for this specific stat
  const statData = statsData.value[item.id];
  if (!statData) return null;
  
  // Extract the value using the dataKey or fallback to known fields
  return statData[item.dataKey] || statData.value || statData.count || null;
};

// Calculate grid positioning for the item
const getItemStyle = (item) => {
  return {
    // Optional: Add any custom styling for the stat box container
  };
};

// Watch for changes to the widget data
watch(() => props.widgetData, () => {
  fetchGroupData();
}, { deep: true });

// Initial data fetch
onMounted(() => {
  fetchGroupData();
});
</script>

<style scoped lang="scss">
	@import "@/themes/generated/variables.additional.scss";

.stat-box-group {
	display: flex;
	flex:auto;

	gap: 20px;
}

.stat-box-container {
  background-color: rgba($base-bg-dark, 0.3);
		border: 1px solid $base-border-color;
		color: rgba($base-text-color, .65)!important;
		border-radius: 8px;
		padding: 20px;
		overflow: hidden;
		flex:auto;
}
</style>