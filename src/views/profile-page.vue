<template>
  <div>
    <DxLoadPanel
      :height="500"
      :width="700"
      :visible="loadingVisible"
      :show-indicator="true"
      :show-pane="true"
      :shading="true"
      shading-color="rgba(0,0,0,0.4)"
    />
    <!-- <h2 class="content-block">{{userInfo.first_name}}'s Profile</h2> -->
    <div class="content-block dx-card responsive-paddings">
      <p>
        <b>If any of the below items need to be changed contact your system administrator.</b><br>
        <b>Name:</b> {{userInfo.first_name}} {{userInfo.last_name}}<br>
        <b>Email:</b> {{userInfo.email}}<br>
        <b>Company:</b> {{userInfo.company_name}}<br>
        <b>User Type:</b> {{userInfo.user_type}}<br>
        <b>Security Level:</b> {{userInfo.security_level}}<br>
        <br /><br /><b>Work In Progress</b>
      </p>
    </div>


  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import auth2 from '../auth2';
import notify from 'devextreme/ui/notify';
import databaseName from '../myFunctions/databaseName';
import { DxLoadPanel } from 'devextreme-vue/load-panel';

const router = useRouter();
const userInfo = ref({});
const loadingVisible = ref(false);

// Initialize the page
onMounted(async () => {
  loadingVisible.value = true;

  try {
    // Check website version
    databaseName.checkWebsiteVersion();

    // Check if user is logged in using the new auth2 system
    if (!auth2.loggedIn()) {
      notify('Please Log In To Use The Service Site', 'warning', 10000);
      router.push('/login-form');
      return;
    }

    // Get user data
    const userResult = await auth2.getUser();

    if (userResult.isOk) {
      userInfo.value = userResult.data;

      // Check authorization level (this still uses the old system for now)
      if (databaseName.validateAuthLevel('create_modify')) {
        console.log('✅ User authorized for profile page');
        console.log('👤 User data:', userInfo.value);
      } else {
        notify('Your Account Does NOT Have Access To This Page', 'warning', 10000);
        router.push('/home');
      }
    } else {
      notify('Failed to get user information', 'error', 5000);
      router.push('/login-form');
    }
  } catch (error) {
    console.error('Error loading profile page:', error);
    notify('An error occurred while loading your profile', 'error', 5000);
    router.push('/home');
  } finally {
    loadingVisible.value = false;
  }
});

</script>

<style lang="scss">
#changePassForm{
  width: 50%;
  position: relative;
  margin: 20px auto;
  padding: 20px 10px 10px 10px;
  border: 2px solid #3aafa9;
  border-radius: 25px;
  height: auto;
}
</style>