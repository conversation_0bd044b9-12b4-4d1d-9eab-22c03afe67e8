<template>
	<div class="content-block dx-card responsive-paddings">
		<div class="eclipse-container">
			<div class="sun-highlight"></div>
    <div class="eclipse"></div>
    <div class="coming-soon">COMING SOON</div>
    <div class="progress-container">
      <div class="progress-bar"></div>
    </div>
  </div>
	</div>
</template>

<style lang="scss" scoped>
  @import "../themes/generated/variables.additional.scss";

	.content-block {
    color: rgba($base-text-color, 0.65)!important;
    display: flex;
    flex-direction: column;
    flex: 1 1 auto;
    min-height: 0;
    
    overflow: hidden;
    position: relative;
    transition: all 0.3s ease;
		justify-content: center;
		align-items: center;
	}

	.eclipse-container {
		position: relative;
		width: 500px;
		height: 500px;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}
	
	.eclipse {
		position: absolute;
		width: 300px;
		height: 300px;
		border-radius: 50%;
		background-color: #363641;
		box-shadow: 8px -16px 13px 0px rgba(255, 150, 150, 0.3);
		z-index: 1;
		}
	
	.sun-highlight {
		position: absolute;
		width: 104px;
		height: 76px;
		border-radius: 50%;
		z-index: 0;
		top: 127px;
		right: 112px;
		background: radial-gradient(circle at center, rgb(255, 200, 200) 0%, rgb(255, 150, 150) 40%, rgba(74, 74, 74, 0) 100%);
		filter: blur(5px);
		box-shadow: 0px 0px 0px 0px rgba(255, 150, 150, 0.5);
		transform: rotate(45deg);
		
	}
	
	.coming-soon {
		color: white;
		letter-spacing: 10px;
		font-size: 32px;
		margin-bottom: 40px;
		font-weight: 300;
		z-index: 2;
		text-align: center;
	}
	
	.progress-container {
		width: 200px;
		height: 6px;
		background-color: rgba(255, 255, 255, 0.2);
		border-radius: 3px;
		margin-top: 10px;
		z-index: 2;
		overflow: hidden;
	}
	
	.progress-bar {
		width: 40%;
		height: 100%;
		background: linear-gradient(to right, #f27181, #e9383f);
		border-radius: 3px;
		animation: pulse 2s infinite;
	}
    
</style>