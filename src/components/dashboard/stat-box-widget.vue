<template>
	<div class="widget-wrap">
    <div class="widget-header">
      <div class="widget-title">{{ widgetData.title }}</div>
    </div>
    <div class="widget-body">
      <div v-if="loading" class="widget-loading">Loading...</div>
      <div v-else-if="error" class="widget-error">{{ error }}</div>
      
			<div v-else class="widget-container">
				<span class="widget-value">{{ formatValue(statValue, dataType, format) }}</span>
      </div>

    </div>
  </div>
</template>

<script setup>
	import { ref, computed, onMounted, watch, defineProps } from 'vue';
	import { useFormatHelper } from '@/composables/useFormatHelper';

	const { formatValue } = useFormatHelper();

	const props = defineProps({
		widgetData: {
			type: Object,
			required: true
		},
		statValue: {
      type: [Number, String, Object],
      default: null
    },
		dataType: {
      type: String,
      default: 'number'
    },
		format: {
      type: Object,
      default: () => {}
    }
	});

	const loading = ref(false);
	const error = ref(null);

</script>

<style scoped lang="scss">
	@import "@/themes/generated/variables.additional.scss";

	.widget-wrap {
		height: 100%;
		display: flex;
		flex-direction: column;
	}
	.widget-container {
		height: 100%;
	}
	.widget-body {
		height: 100%;
	}
	.widget-title {
		font-size: 16px;
		font-weight: 400;
		margin-bottom: 10px;
	}

	.widget-value {
		font-size: 18px;
		font-weight: 500;
		margin: 0;
	}


</style>